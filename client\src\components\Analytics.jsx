import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// Simple analytics tracking
class Analytics {
  constructor() {
    this.events = [];
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
  }

  generateSessionId() {
    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  track(eventName, properties = {}) {
    const event = {
      id: Math.random().toString(36).substr(2, 9),
      name: eventName,
      properties: {
        ...properties,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        url: window.location.href,
        userAgent: navigator.userAgent,
        referrer: document.referrer,
      }
    };

    this.events.push(event);
    
    // Store in localStorage for persistence
    try {
      const storedEvents = JSON.parse(localStorage.getItem('portfolio_analytics') || '[]');
      storedEvents.push(event);
      
      // Keep only last 100 events to prevent storage bloat
      if (storedEvents.length > 100) {
        storedEvents.splice(0, storedEvents.length - 100);
      }
      
      localStorage.setItem('portfolio_analytics', JSON.stringify(storedEvents));
    } catch (error) {
      console.warn('Failed to store analytics event:', error);
    }

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Event:', event);
    }
  }

  pageView(path, title) {
    this.track('page_view', {
      path,
      title,
      loadTime: Date.now() - this.startTime
    });
  }

  buttonClick(buttonName, location) {
    this.track('button_click', {
      buttonName,
      location
    });
  }

  formSubmit(formName, success = true) {
    this.track('form_submit', {
      formName,
      success
    });
  }

  projectView(projectId, projectTitle) {
    this.track('project_view', {
      projectId,
      projectTitle
    });
  }

  downloadCV() {
    this.track('cv_download', {
      downloadTime: new Date().toISOString()
    });
  }

  socialMediaClick(platform, url) {
    this.track('social_media_click', {
      platform,
      url
    });
  }

  searchPerformed(query, resultsCount) {
    this.track('search', {
      query,
      resultsCount
    });
  }

  timeOnPage(path, timeSpent) {
    this.track('time_on_page', {
      path,
      timeSpent
    });
  }

  getEvents() {
    try {
      return JSON.parse(localStorage.getItem('portfolio_analytics') || '[]');
    } catch {
      return [];
    }
  }

  clearEvents() {
    localStorage.removeItem('portfolio_analytics');
    this.events = [];
  }

  // Get basic analytics summary
  getSummary() {
    const events = this.getEvents();
    const pageViews = events.filter(e => e.name === 'page_view');
    const buttonClicks = events.filter(e => e.name === 'button_click');
    const formSubmissions = events.filter(e => e.name === 'form_submit');
    
    return {
      totalEvents: events.length,
      pageViews: pageViews.length,
      buttonClicks: buttonClicks.length,
      formSubmissions: formSubmissions.length,
      mostVisitedPages: this.getMostVisitedPages(pageViews),
      popularButtons: this.getPopularButtons(buttonClicks)
    };
  }

  getMostVisitedPages(pageViews) {
    const pageCounts = {};
    pageViews.forEach(event => {
      const path = event.properties.path;
      pageCounts[path] = (pageCounts[path] || 0) + 1;
    });
    
    return Object.entries(pageCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([path, count]) => ({ path, count }));
  }

  getPopularButtons(buttonClicks) {
    const buttonCounts = {};
    buttonClicks.forEach(event => {
      const buttonName = event.properties.buttonName;
      buttonCounts[buttonName] = (buttonCounts[buttonName] || 0) + 1;
    });
    
    return Object.entries(buttonCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([buttonName, count]) => ({ buttonName, count }));
  }
}

// Create global analytics instance
const analytics = new Analytics();

// Page view tracker component
export const PageTracker = () => {
  const location = useLocation();

  useEffect(() => {
    const startTime = Date.now();
    
    // Track page view
    analytics.pageView(location.pathname, document.title);

    // Track time on page when leaving
    return () => {
      const timeSpent = Date.now() - startTime;
      analytics.timeOnPage(location.pathname, timeSpent);
    };
  }, [location]);

  return null;
};

// HOC for tracking button clicks
export const withAnalytics = (WrappedComponent) => {
  return function AnalyticsWrapper(props) {
    const handleClick = (originalOnClick, buttonName) => {
      return (event) => {
        analytics.buttonClick(buttonName, window.location.pathname);
        if (originalOnClick) {
          originalOnClick(event);
        }
      };
    };

    const enhancedProps = {
      ...props,
      onClick: props.trackAs ? handleClick(props.onClick, props.trackAs) : props.onClick
    };

    return <WrappedComponent {...enhancedProps} />;
  };
};

// Hook for manual tracking
export const useAnalytics = () => {
  return {
    track: analytics.track.bind(analytics),
    pageView: analytics.pageView.bind(analytics),
    buttonClick: analytics.buttonClick.bind(analytics),
    formSubmit: analytics.formSubmit.bind(analytics),
    projectView: analytics.projectView.bind(analytics),
    downloadCV: analytics.downloadCV.bind(analytics),
    socialMediaClick: analytics.socialMediaClick.bind(analytics),
    searchPerformed: analytics.searchPerformed.bind(analytics),
    getSummary: analytics.getSummary.bind(analytics),
    getEvents: analytics.getEvents.bind(analytics),
    clearEvents: analytics.clearEvents.bind(analytics)
  };
};

export default analytics;
