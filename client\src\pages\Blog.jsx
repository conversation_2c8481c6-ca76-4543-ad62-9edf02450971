import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaClock, FaTags, FaVideo } from 'react-icons/fa';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import { format } from 'date-fns';

const API_URL = 'https://portfolio-s11i.onrender.com';

const getImageUrl = (path) => {
  // Handle null, undefined or non-string values
  if (!path) return '/vite.svg';
  
  // Handle the case where path is an object with url property (coverImage structure)
  if (typeof path === 'object' && path.url) {
    path = path.url;
  }
  
  // If path is still not a string after handling object case, return default image
  if (typeof path !== 'string') return '/vite.svg';
  
  // If it's already a full URL, return it as is
  if (path.startsWith('http')) return path;
  
  // Fix path if it contains '/images/' incorrectly
  if (path.includes('/uploads/images/')) {
    path = path.replace('/uploads/images/', '/uploads/');
  }
  
  // If the path already includes the API_URL, don't add it again
  if (path.includes(API_URL)) {
    return path;
  }
  
  // Ensure proper path formatting with leading slash
  return `${API_URL}${path.startsWith('/') ? '' : '/'}${path}`;
};

export default function Blog() {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_URL}/api/blogs`);
        if (!response.ok) {
          throw new Error('Failed to fetch posts');
        }
        const data = await response.json();
        console.log('Fetched posts:', data); // Debug log
        setPosts(data.blogs || []); // Access the blogs array from the response
      } catch (err) {
        console.error('Error fetching posts:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
        <ErrorMessage message={error} />
      </div>
    );
  }

  return (
    <div className="min-h-screen mt-12 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 py-16 sm:py-20 w-full">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl font-bold text-center text-gray-900 dark:text-white mb-8 sm:mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Blog</span> Posts
        </motion.h1>

        {/* Blog Posts Grid */}
        <div className="grid w-full grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8">
          <AnimatePresence>
            {posts.map((post, index) => (
              <motion.article
                key={post._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
              >
                <div className="relative h-48 sm:h-56 overflow-hidden">
                  <img
                    src={getImageUrl(post.coverImage)}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    onError={(e) => {
                      e.target.src = '/vite.svg';
                      console.log('Image failed to load:', post.coverImage); // Debug log
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  {post.video && post.video.url && (
                    <motion.div
                      className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white p-2 rounded-lg"
                      whileHover={{ scale: 1.1 }}
                    >
                      <FaVideo className="w-4 h-4" />
                    </motion.div>
                  )}
                </div>
                <div className="p-4 sm:p-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-3">
                    <FaClock className="w-4 h-4" />
                    <span>
                      {format(new Date(post.createdAt || post.publishedAt || Date.now()), 'MMM d, yyyy')}
                    </span>
                  </div>
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors line-clamp-2">
                    {post.title}
                  </h2>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed">
                    {post.excerpt || (post.content?.substring(0, 150) + '...') || 'No description available'}
                  </p>
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex items-center gap-2 mb-4">
                      <FaTags className="w-4 h-4 text-gray-400" />
                      <div className="flex flex-wrap gap-2">
                        {post.tags.map((tag) => (
                          <span
                            key={tag}
                            className="text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      By {post.author || 'Anonymous'}
                    </span>
                    <Link
                      to={`/blog/${post.slug}`}
                      className="text-primary hover:text-primary-dark transition-colors duration-300"
                    >
                      Read More →
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </AnimatePresence>
        </div>

        {/* No Posts Found */}
        {posts.length === 0 && !loading && !error && (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-300 text-lg">
              No blog posts available at the moment.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
