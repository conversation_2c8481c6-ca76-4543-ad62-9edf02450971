import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaClock, FaTags, FaVideo } from 'react-icons/fa';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import { format } from 'date-fns';

const API_URL = 'https://portfolio-s11i.onrender.com';

const getImageUrl = (path) => {
  // Handle null, undefined or non-string values
  if (!path) return '/vite.svg';
  
  // Handle the case where path is an object with url property (coverImage structure)
  if (typeof path === 'object' && path.url) {
    path = path.url;
  }
  
  // If path is still not a string after handling object case, return default image
  if (typeof path !== 'string') return '/vite.svg';
  
  // If it's already a full URL, return it as is
  if (path.startsWith('http')) return path;
  
  // Fix path if it contains '/images/' incorrectly
  if (path.includes('/uploads/images/')) {
    path = path.replace('/uploads/images/', '/uploads/');
  }
  
  // If the path already includes the API_URL, don't add it again
  if (path.includes(API_URL)) {
    return path;
  }
  
  // Ensure proper path formatting with leading slash
  return `${API_URL}${path.startsWith('/') ? '' : '/'}${path}`;
};

export default function Blog() {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_URL}/api/blogs`);
        if (!response.ok) {
          throw new Error('Failed to fetch posts');
        }
        const data = await response.json();
        console.log('Fetched posts:', data); // Debug log
        setPosts(data.blogs || []); // Access the blogs array from the response
      } catch (err) {
        console.error('Error fetching posts:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
        <ErrorMessage message={error} />
      </div>
    );
  }

  return (
    <div className="min-h-screen mt-12 bg-gray-50 dark:bg-gray-900 py-16 w-full">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl font-bold text-center text-gray-900 dark:text-white mb-8">
          Blog Posts
        </h1>

        {/* Blog Posts Grid */}
        <div className="grid w-full grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
          <AnimatePresence>
            {posts.map((post) => (
              <motion.article
                key={post._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                <div className="relative">
                  <img
                    src={getImageUrl(post.coverImage)}
                    alt={post.title}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                      e.target.src = '/vite.svg';
                      console.log('Image failed to load:', post.coverImage); // Debug log
                    }}
                  />
                  {post.video && post.video.url && (
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white p-1 rounded-md">
                      <FaVideo className="w-4 h-4" />
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <FaClock className="w-4 h-4" />
                    <span>
                      {format(new Date(post.createdAt || post.publishedAt || Date.now()), 'MMM d, yyyy')}
                    </span>
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {post.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {post.excerpt || (post.content?.substring(0, 150) + '...') || 'No description available'}
                  </p>
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex items-center gap-2 mb-4">
                      <FaTags className="w-4 h-4 text-gray-400" />
                      <div className="flex flex-wrap gap-2">
                        {post.tags.map((tag) => (
                          <span
                            key={tag}
                            className="text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      By {post.author || 'Anonymous'}
                    </span>
                    <Link
                      to={`/blog/${post.slug}`}
                      className="text-primary hover:text-primary-dark transition-colors duration-300"
                    >
                      Read More →
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </AnimatePresence>
        </div>

        {/* No Posts Found */}
        {posts.length === 0 && !loading && !error && (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-300 text-lg">
              No blog posts available at the moment.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
