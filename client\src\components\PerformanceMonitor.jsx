import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Performance monitoring utilities
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      cumulativeLayoutShift: 0,
      firstInputDelay: 0,
      timeToInteractive: 0
    };
    
    this.observers = [];
    this.init();
  }

  init() {
    // Wait for page to load
    if (document.readyState === 'complete') {
      this.measureMetrics();
    } else {
      window.addEventListener('load', () => this.measureMetrics());
    }
  }

  measureMetrics() {
    // Page Load Time
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
    }

    // Web Vitals
    this.measureWebVitals();
    
    // Custom metrics
    this.measureCustomMetrics();
  }

  measureWebVitals() {
    // First Contentful Paint
    const paintEntries = performance.getEntriesByType('paint');
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    if (fcpEntry) {
      this.metrics.firstContentfulPaint = fcpEntry.startTime;
    }

    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.metrics.largestContentfulPaint = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP measurement not supported');
      }

      // Cumulative Layout Shift
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          this.metrics.cumulativeLayoutShift = clsValue;
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS measurement not supported');
      }

      // First Input Delay
      try {
        const fidObserver = new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID measurement not supported');
      }
    }
  }

  measureCustomMetrics() {
    // Time to Interactive (simplified)
    setTimeout(() => {
      this.metrics.timeToInteractive = performance.now();
    }, 100);
  }

  getMetrics() {
    return { ...this.metrics };
  }

  getGrade() {
    const { pageLoadTime, firstContentfulPaint, largestContentfulPaint, cumulativeLayoutShift } = this.metrics;
    
    let score = 100;
    
    // Page Load Time scoring
    if (pageLoadTime > 3000) score -= 20;
    else if (pageLoadTime > 2000) score -= 10;
    
    // FCP scoring
    if (firstContentfulPaint > 2500) score -= 15;
    else if (firstContentfulPaint > 1800) score -= 8;
    
    // LCP scoring
    if (largestContentfulPaint > 4000) score -= 20;
    else if (largestContentfulPaint > 2500) score -= 10;
    
    // CLS scoring
    if (cumulativeLayoutShift > 0.25) score -= 15;
    else if (cumulativeLayoutShift > 0.1) score -= 8;
    
    if (score >= 90) return { grade: 'A', color: 'green' };
    if (score >= 80) return { grade: 'B', color: 'yellow' };
    if (score >= 70) return { grade: 'C', color: 'orange' };
    return { grade: 'D', color: 'red' };
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Performance display component
export const PerformanceIndicator = ({ show = false }) => {
  const [metrics, setMetrics] = useState(null);
  const [monitor] = useState(() => new PerformanceMonitor());

  useEffect(() => {
    const updateMetrics = () => {
      setMetrics(monitor.getMetrics());
    };

    // Update metrics periodically
    const interval = setInterval(updateMetrics, 1000);
    
    // Initial update after a delay
    setTimeout(updateMetrics, 2000);

    return () => {
      clearInterval(interval);
      monitor.cleanup();
    };
  }, [monitor]);

  if (!show || !metrics) return null;

  const grade = monitor.getGrade();

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 100 }}
        className="fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 max-w-sm"
      >
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
            Performance
          </h3>
          <div className={`px-2 py-1 rounded text-xs font-bold text-white bg-${grade.color}-500`}>
            {grade.grade}
          </div>
        </div>
        
        <div className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Load Time:</span>
            <span className="font-mono">{Math.round(metrics.pageLoadTime)}ms</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">FCP:</span>
            <span className="font-mono">{Math.round(metrics.firstContentfulPaint)}ms</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">LCP:</span>
            <span className="font-mono">{Math.round(metrics.largestContentfulPaint)}ms</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">CLS:</span>
            <span className="font-mono">{metrics.cumulativeLayoutShift.toFixed(3)}</span>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

// Hook for performance monitoring
export const usePerformance = () => {
  const [monitor] = useState(() => new PerformanceMonitor());
  
  useEffect(() => {
    return () => monitor.cleanup();
  }, [monitor]);

  return {
    getMetrics: () => monitor.getMetrics(),
    getGrade: () => monitor.getGrade()
  };
};

// Resource loading monitor
export const ResourceMonitor = () => {
  const [resources, setResources] = useState([]);

  useEffect(() => {
    const updateResources = () => {
      const entries = performance.getEntriesByType('resource');
      const slowResources = entries
        .filter(entry => entry.duration > 1000)
        .map(entry => ({
          name: entry.name.split('/').pop(),
          duration: Math.round(entry.duration),
          size: entry.transferSize || 0
        }))
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 5);
      
      setResources(slowResources);
    };

    setTimeout(updateResources, 3000);
  }, []);

  if (resources.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 left-4 z-50 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 max-w-xs"
    >
      <h4 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
        Slow Resources
      </h4>
      <div className="space-y-1">
        {resources.map((resource, index) => (
          <div key={index} className="text-xs text-yellow-700 dark:text-yellow-300">
            <div className="truncate">{resource.name}</div>
            <div className="text-yellow-600 dark:text-yellow-400">
              {resource.duration}ms • {Math.round(resource.size / 1024)}KB
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default PerformanceMonitor;
