import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaBriefcase, FaDownload, FaCode, FaPalette, FaMobile } from 'react-icons/fa';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const timeline = [
  {
    year: '2025',
    title: 'Full-Stack Developer & Project Lead',
    company: 'Arsi University',
    description: 'Leading development of enterprise web applications using MERN stack. Architecting scalable solutions and mentoring junior developers in modern web development practices.',
    icon: FaBriefcase,
  },
  {
    year: '2024-2025',
    title: 'UX/UI Designer',
    company: 'Ethronics IRAS',
    description: 'Designing intuitive user interfaces using Figma and modern design principles. Creating design systems that bridge the gap between user needs and technical implementation, resulting in 40% improved user engagement.',
    icon: FaBriefcase,
  },
  {
    year: '2022-2025',
    title: 'Computer Science Student',
    company: 'Arsi University',
    description: 'Pursuing Bachelor\'s degree in Computer Science with focus on Software Engineering, Data Structures, and Web Technologies. Maintaining excellent academic performance while working on real-world projects.',
    icon: FaGraduationCap,
  },
  {
    year: '2023',
    title: 'Freelance Web Developer',
    company: 'Independent',
    description: 'Developed custom websites and web applications for local businesses. Specialized in responsive design, performance optimization, and modern JavaScript frameworks.',
    icon: FaBriefcase,
  },
];

const skills = [
  { name: 'Frontend Development', icon: FaCode, level: 92, technologies: 'React, Vue.js, TypeScript, Tailwind CSS' },
  { name: 'Backend Development', icon: FaCode, level: 85, technologies: 'Node.js, Express, MongoDB, PostgreSQL' },
  { name: 'UI/UX Design', icon: FaPalette, level: 88, technologies: 'Figma, Adobe XD, Prototyping, User Research' },
  { name: 'Mobile Development', icon: FaMobile, level: 78, technologies: 'React Native, Flutter, iOS/Android' },
];

export default function About() {
  const timelineRef = useRef();
  const skillsRef = useRef();

  useEffect(() => {
    const timeline = timelineRef.current;
    const skills = skillsRef.current;

    gsap.from(skills.children, {
      width: 0,
      stagger: 0.2,
      duration: 1,
      scrollTrigger: {
        trigger: skills,
        start: 'top center+=100',
        toggleActions: 'play none none reverse',
      },
    });
  }, []);

  return (
    <div className="min-h-screen mt-12 bg-gradient-to-br from-violet-50 to-indigo-50 dark:from-gray-900 dark:to-indigo-950 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-12 mb-20">
          <div className="lg:w-1/2 relative">
            <div className="relative inline-block">
              <div className="w-72 h-90 md:w-96 md:h-100 rounded-2xl overflow-hidden border-4 border-indigo-500 dark:border-indigo-400 shadow-2xl">
                <img
                  src="/profile/about.jpg"
                  alt="Bosona Gosaye"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
          
          <div className="lg:w-1/2">
            <h1 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white">
              About <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-violet-500 dark:from-indigo-400 dark:to-violet-300">Me</span>
            </h1>
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
              I'm a passionate Full-Stack Developer, UX/UI Designer, and Mobile App Developer currently pursuing my Computer Science degree at Arsi University. With a strong foundation in modern web technologies and a keen eye for design, I specialize in creating seamless digital experiences that bridge the gap between functionality and aesthetics.
            </p>
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
              My expertise spans across the entire development lifecycle - from conceptualizing user-centered designs in Figma to implementing robust full-stack solutions using the MERN stack. I'm particularly passionate about creating responsive, accessible applications that deliver exceptional user experiences across all devices.
            </p>
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
              When I'm not coding, you'll find me exploring the latest design trends, contributing to open-source projects, or mentoring fellow developers. I believe in continuous learning and staying at the forefront of technology to deliver innovative solutions that make a real impact.
            </p>
            <a
              href="/resume.pdf"
              download
              className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-indigo-600 to-violet-500 hover:from-indigo-700 hover:to-violet-600 text-white font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <FaDownload className="mr-2" />
              Download CV
            </a>
          </div>
        </div>

        <div className="mb-20">
          <h2 className="text-3xl font-bold mb-10 text-center text-gray-900 dark:text-white">
            Professional <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-violet-500 dark:from-indigo-400 dark:to-violet-300">Timeline</span>
          </h2>
          <div ref={timelineRef} className="relative">
            {timeline.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 1, x: 0 }}
                className="flex gap-4 mb-8"
              >
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-violet-500 dark:from-indigo-600 dark:to-violet-600 flex items-center justify-center text-green shadow-lg">
                  <item.icon className="w-6 h-6" />
                </div>
                <div className="flex-grow">
                  <div className="flex items-center gap-4 mb-2">
                    <span className="text-sm font-semibold px-3 py-1 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300">
                      {item.year}
                    </span>
                    <h3 className="text-xl font-bold text-blue-900 dark:text-blue">
                      {item.title}
                    </h3>
                  </div>
                  <p className="text-lg font-medium text-indigo-600 dark:text-indigo-400 mb-1">
                    {item.company}
                  </p>
                  <p className="text-green-600 dark:text-green-400">
                    {item.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-3xl font-bold mb-10 text-center text-gray-900 dark:text-white">
            Technical <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-violet-500 dark:from-indigo-400 dark:to-violet-300">Skills</span>
          </h2>
          <div ref={skillsRef} className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {skills.map((skill, index) => (
              <motion.div
                key={index}
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ delay: index * 0.2 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-violet-500 dark:from-indigo-600 dark:to-violet-600 flex items-center justify-center text-white">
                    <skill.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {skill.name}
                  </h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {skill.technologies}
                </p>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-indigo-500 to-violet-500 dark:from-indigo-600 dark:to-violet-600 rounded-full"
                    style={{ width: `${skill.level}%` }}
                  />
                </div>
                <div className="mt-2 text-right text-sm font-medium text-gray-600 dark:text-gray-400">
                  {skill.level}%
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
