import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaBriefcase, FaDownload, FaCode, FaPalette, FaMobile } from 'react-icons/fa';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import SEO from '../components/SEO';

gsap.registerPlugin(ScrollTrigger);

const timeline = [
  {
    year: '2025',
    title: 'Full-Stack Developer & Project Lead',
    company: 'Arsi University',
    description: 'Leading development of enterprise web applications using MERN stack. Architecting scalable solutions and mentoring junior developers in modern web development practices.',
    icon: FaBriefcase,
  },
  {
    year: '2024-2025',
    title: 'UX/UI Designer',
    company: 'Ethronics IRAS',
    description: 'Designing intuitive user interfaces using Figma and modern design principles. Creating design systems that bridge the gap between user needs and technical implementation, resulting in 40% improved user engagement.',
    icon: FaBriefcase,
  },
  {
    year: '2022-2025',
    title: 'Computer Science Student',
    company: 'Arsi University',
    description: 'Pursuing Bachelor\'s degree in Computer Science with focus on Software Engineering, Data Structures, and Web Technologies. Maintaining excellent academic performance while working on real-world projects.',
    icon: FaGraduationCap,
  },
  {
    year: '2023',
    title: 'Freelance Web Developer',
    company: 'Independent',
    description: 'Developed custom websites and web applications for local businesses. Specialized in responsive design, performance optimization, and modern JavaScript frameworks.',
    icon: FaBriefcase,
  },
];

const skills = [
  { name: 'Frontend Development', icon: FaCode, level: 92, technologies: 'React, Vue.js, TypeScript, Tailwind CSS' },
  { name: 'Backend Development', icon: FaCode, level: 85, technologies: 'Node.js, Express, MongoDB, PostgreSQL' },
  { name: 'UI/UX Design', icon: FaPalette, level: 88, technologies: 'Figma, Adobe XD, Prototyping, User Research' },
  { name: 'Mobile Development', icon: FaMobile, level: 78, technologies: 'React Native, Flutter, iOS/Android' },
];

export default function About() {
  const timelineRef = useRef();
  const skillsRef = useRef();

  useEffect(() => {
    const timeline = timelineRef.current;
    const skills = skillsRef.current;

    gsap.from(skills.children, {
      width: 0,
      stagger: 0.2,
      duration: 1,
      scrollTrigger: {
        trigger: skills,
        start: 'top center+=100',
        toggleActions: 'play none none reverse',
      },
    });
  }, []);

  return (
    <>
      <SEO
        title="About"
        description="Learn about Bosona Gosaye - Full-Stack Developer and UX/UI Designer with expertise in MERN stack, React, Node.js, and modern web technologies. Currently studying Computer Science at Arsi University."
        keywords={['about', 'biography', 'experience', 'education', 'skills']}
      />

      <div className="min-h-screen mt-12 bg-gradient-to-br from-violet-50 to-indigo-50 dark:from-gray-900 dark:to-indigo-950 py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-8 sm:gap-12 lg:gap-16 mb-16 sm:mb-20">
          <motion.div
            className="lg:w-1/2 relative"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="relative inline-block">
              <motion.div
                className="w-64 h-80 sm:w-72 sm:h-90 md:w-80 md:h-96 lg:w-72 lg:h-90 xl:w-96 xl:h-100 rounded-2xl overflow-hidden border-4 border-indigo-500 dark:border-indigo-400 shadow-2xl"
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ duration: 0.3 }}
              >
                <img
                  src="/profile/about.jpg"
                  alt="Bosona Gosaye"
                  className="w-full h-full object-cover"
                />
              </motion.div>
              {/* Floating decorative elements */}
              <motion.div
                animate={{ y: [-10, 10, -10], rotate: [0, 180, 360] }}
                transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -top-4 -right-4 w-8 h-8 bg-indigo-500 rounded-full opacity-60"
              />
              <motion.div
                animate={{ y: [10, -10, 10], rotate: [360, 180, 0] }}
                transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -bottom-6 -left-6 w-6 h-6 bg-purple-500 rounded-full opacity-60"
              />
            </div>
          </motion.div>

          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 sm:mb-8 text-gray-900 dark:text-white">
              About <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-violet-500 dark:from-indigo-400 dark:to-violet-300">Me</span>
            </h1>
            <motion.p
              className="text-base sm:text-lg text-gray-700 dark:text-gray-300 mb-4 sm:mb-6 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              I'm a passionate Full-Stack Developer, UX/UI Designer, and Mobile App Developer currently pursuing my Computer Science degree at Arsi University. With a strong foundation in modern web technologies and a keen eye for design, I specialize in creating seamless digital experiences that bridge the gap between functionality and aesthetics.
            </motion.p>
            <motion.p
              className="text-base sm:text-lg text-gray-700 dark:text-gray-300 mb-4 sm:mb-6 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              My expertise spans across the entire development lifecycle - from conceptualizing user-centered designs in Figma to implementing robust full-stack solutions using the MERN stack. I'm particularly passionate about creating responsive, accessible applications that deliver exceptional user experiences across all devices.
            </motion.p>
            <motion.p
              className="text-base sm:text-lg text-gray-700 dark:text-gray-300 mb-6 sm:mb-8 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              When I'm not coding, you'll find me exploring the latest design trends, contributing to open-source projects, or mentoring fellow developers. I believe in continuous learning and staying at the forefront of technology to deliver innovative solutions that make a real impact.
            </motion.p>
            <motion.a
              href="/resume.pdf"
              download
              className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 rounded-full bg-gradient-to-r from-indigo-600 to-violet-500 hover:from-indigo-700 hover:to-violet-600 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <FaDownload className="mr-2" />
              Download CV
            </motion.a>
          </motion.div>
        </div>

        <div className="mb-16 sm:mb-20">
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-8 sm:mb-12 text-center text-gray-900 dark:text-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Professional <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-violet-500 dark:from-indigo-400 dark:to-violet-300">Timeline</span>
          </motion.h2>
          <div ref={timelineRef} className="relative">
            {timeline.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex gap-4 sm:gap-6 mb-8 sm:mb-12 group"
              >
                <motion.div
                  className="flex-shrink-0 w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-gradient-to-br from-indigo-500 to-violet-500 dark:from-indigo-600 dark:to-violet-600 flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                  whileHover={{ scale: 1.1, rotate: 360 }}
                  transition={{ duration: 0.3 }}
                >
                  <item.icon className="w-6 h-6 sm:w-7 sm:h-7" />
                </motion.div>
                <div className="flex-grow">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-3">
                    <span className="text-sm font-semibold px-3 py-1 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 w-fit">
                      {item.year}
                    </span>
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                      {item.title}
                    </h3>
                  </div>
                  <p className="text-base sm:text-lg font-medium text-indigo-600 dark:text-indigo-400 mb-2">
                    {item.company}
                  </p>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <div>
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-8 sm:mb-12 text-center text-gray-900 dark:text-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Technical <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-violet-500 dark:from-indigo-400 dark:to-violet-300">Skills</span>
          </motion.h2>
          <div ref={skillsRef} className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8">
            {skills.map((skill, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                viewport={{ once: true }}
                className="group bg-white dark:bg-gray-800 rounded-xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
              >
                <div className="flex items-center gap-4 mb-4 sm:mb-6">
                  <motion.div
                    className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-gradient-to-br from-indigo-500 to-violet-500 dark:from-indigo-600 dark:to-violet-600 flex items-center justify-center text-white group-hover:shadow-lg transition-shadow"
                    whileHover={{ scale: 1.1, rotate: 360 }}
                    transition={{ duration: 0.3 }}
                  >
                    <skill.icon className="w-6 h-6 sm:w-7 sm:h-7" />
                  </motion.div>
                  <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                    {skill.name}
                  </h3>
                </div>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-4 sm:mb-6 leading-relaxed">
                  {skill.technologies}
                </p>
                <div className="h-3 sm:h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-indigo-500 to-violet-500 dark:from-indigo-600 dark:to-violet-600 rounded-full"
                    initial={{ width: 0 }}
                    whileInView={{ width: `${skill.level}%` }}
                    transition={{ duration: 1, delay: index * 0.2 }}
                    viewport={{ once: true }}
                  />
                </div>
                <div className="mt-2 sm:mt-3 text-right text-sm sm:text-base font-bold text-gray-600 dark:text-gray-400">
                  {skill.level}%
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
