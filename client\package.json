{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "vercel-build": "npm run build"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.6.2", "date-fns": "^4.1.0", "framer-motion": "^10.18.0", "gsap": "^3.12.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.4", "react-icons": "^4.12.0", "react-intersection-observer": "^9.5.3", "react-markdown": "^9.0.1", "react-quill": "^2.0.0", "react-router-dom": "^6.21.1", "react-syntax-highlighter": "^15.5.0", "react-toastify": "^11.0.2", "react-use": "^17.6.0", "sharp": "^0.33.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jest": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^5.0.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1"}}