import React from 'react';
import { Link } from 'react-router-dom';
import { useSettings } from '../contexts/SettingsContext';

const Header = () => {
  const { settings, loading } = useSettings();

  if (loading) {
    return <div>Loading header...</div>;
  }

  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link to="/" className="flex items-center">
          {settings?.logo?.url ? (
            <img 
              src={settings.logo.url} 
              alt={settings.logo.alt || "Logo"} 
              className="h-10" 
            />
          ) : (
            <h1 className="text-xl font-bold">{settings?.siteTitle || 'My Portfolio'}</h1>
          )}
        </Link>
        
        <nav className="hidden md:flex space-x-6">
          {settings?.navigation?.map((item, index) => (
            <Link 
              key={index} 
              to={item.path} 
              className="text-gray-700 hover:text-indigo-600 transition"
            >
              {item.label}
            </Link>
          )) || (
            <>
              <Link to="/" className="text-gray-700 hover:text-indigo-600 transition">Home</Link>
              <Link to="/about" className="text-gray-700 hover:text-indigo-600 transition">About</Link>
              <Link to="/projects" className="text-gray-700 hover:text-indigo-600 transition">Projects</Link>
              <Link to="/contact" className="text-gray-700 hover:text-indigo-600 transition">Contact</Link>
            </>
          )}
        </nav>
        
        {/* Mobile menu button */}
        <button className="md:hidden">
          {/* SVG for menu icon */}
        </button>
      </div>
    </header>
  );
};

export default Header; 