import api from './api';

const blogsApi = {
  getAllBlogs: async (page = 1, limit = 10) => {
    const response = await api.get(`/blogs/admin?page=${page}&limit=${limit}`);
    return response.data;
  },

  getPublicBlogs: async (page = 1, limit = 10) => {
    const response = await api.get(`/blogs?page=${page}&limit=${limit}`);
    return response.data;
  },

  getFeaturedBlogs: async () => {
    const response = await api.get('/blogs/featured');
    return response.data;
  },

  getBlogBySlug: async (slug) => {
    const response = await api.get(`/blogs/slug/${slug}`);
    return response.data;
  },

  getBlog: async (id) => {
    const response = await api.get(`/blogs/${id}`);
    return response.data;
  },

  createBlog: async (blogData) => {
    const response = await api.post('/blogs', blogData);
    return response.data;
  },

  updateBlog: async (id, blogData) => {
    const response = await api.put(`/blogs/${id}`, blogData);
    return response.data;
  },

  deleteBlog: async (id) => {
    const response = await api.delete(`/blogs/${id}`);
    return response.data;
  }
};

export default blogsApi;
