import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaSave, FaSpinner } from 'react-icons/fa';
import { useSettings } from '../../contexts/SettingsContext';
import ColorPicker from './ColorPicker';
import ImageUpload from './ImageUpload';
import { toast } from 'react-toastify';

export default function SettingsForm() {
  const { settings, updateSettings, loading: contextLoading } = useSettings();
  const [formData, setFormData] = useState(null);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [files, setFiles] = useState({});

  useEffect(() => {
    if (settings) {
      // Ensure contact object with required email field exists
      // and make sure socialLinks is properly structured
      const initialFormData = {
        ...settings,
        contact: {
          ...settings.contact,
          email: settings.contact?.email || '',
          socialLinks: settings.contact?.socialLinks || settings.socialLinks || {}
        },
        // Keep a copy at the top level for the form
        socialLinks: settings.contact?.socialLinks || settings.socialLinks || {}
      };
      setFormData(initialFormData);
    }
  }, [settings]);

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNestedChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleFileChange = (field, file) => {
    setFiles(prev => ({
      ...prev,
      [field]: file
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.contact?.email) {
      toast.error('Contact email is required');
      setActiveTab('contact'); // Switch to contact tab to show the error
      return;
    }
    
    // Prepare data for submission
    const dataToSubmit = {
      ...formData,
      // Remove top-level socialLinks to avoid duplication
      socialLinks: undefined,
      // Ensure contact object exists and properly contains socialLinks
      contact: {
        ...formData.contact,
        socialLinks: formData.socialLinks || {}
      }
    };
    
    setSaving(true);
    try {
      await updateSettings(dataToSubmit, files);
      toast.success('Settings updated successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to update settings');
    }
    setSaving(false);
  };

  const tabs = [
    { id: 'general', label: 'General' },
    { id: 'appearance', label: 'Appearance' },
    { id: 'contact', label: 'Contact' },
    { id: 'social', label: 'Social' },
    { id: 'seo', label: 'SEO' },
    { id: 'about', label: 'About' },
  ];

  if (contextLoading || !formData) {
    return (
      <div className="flex items-center justify-center h-64">
        <FaSpinner className="w-8 h-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="flex space-x-4">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`pb-4 px-1 ${
                activeTab === tab.id
                  ? 'border-b-2 border-indigo-600 text-indigo-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {activeTab === 'general' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Site Title
              </label>
              <input
                type="text"
                value={formData.siteTitle || ''}
                onChange={(e) => handleChange('siteTitle', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description
              </label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => handleChange('description', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                rows="3"
              />
            </div>
            <ImageUpload
              label="Logo"
              currentImage={formData.logo?.url}
              onUpload={(file) => handleFileChange('logo', file)}
              className="mt-4"
            />
            <ImageUpload
              label="Favicon"
              currentImage={formData.favicon}
              onUpload={(file) => handleFileChange('favicon', file)}
              className="mt-4"
            />
          </div>
        )}

        {activeTab === 'appearance' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Primary Color
              </label>
              <ColorPicker
                color={formData.appearance?.primaryColor}
                onChange={(color) => handleNestedChange('appearance', 'primaryColor', color)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Secondary Color
              </label>
              <ColorPicker
                color={formData.appearance?.secondaryColor}
                onChange={(color) => handleNestedChange('appearance', 'secondaryColor', color)}
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={formData.appearance?.darkMode}
                onChange={(e) => handleNestedChange('appearance', 'darkMode', e.target.checked)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Enable Dark Mode by Default
              </label>
            </div>
          </div>
        )}

        {activeTab === 'contact' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                value={formData.contact?.email || ''}
                onChange={(e) => handleNestedChange('contact', 'email', e.target.value)}
                className={`mt-1 block w-full rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${!formData.contact?.email ? 'border-red-500' : 'border-gray-300'}`}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Phone
              </label>
              <input
                type="tel"
                value={formData.contact?.phone || ''}
                onChange={(e) => handleNestedChange('contact', 'phone', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Address
              </label>
              <textarea
                value={formData.contact?.address || ''}
                onChange={(e) => handleNestedChange('contact', 'address', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                rows="3"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Google Maps URL
              </label>
              <input
                type="url"
                value={formData.contact?.mapUrl || ''}
                onChange={(e) => handleNestedChange('contact', 'mapUrl', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
        )}

        {activeTab === 'social' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                GitHub
              </label>
              <input
                type="url"
                value={formData.socialLinks?.github || ''}
                onChange={(e) => handleNestedChange('socialLinks', 'github', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                LinkedIn
              </label>
              <input
                type="url"
                value={formData.socialLinks?.linkedin || ''}
                onChange={(e) => handleNestedChange('socialLinks', 'linkedin', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Twitter
              </label>
              <input
                type="url"
                value={formData.socialLinks?.twitter || ''}
                onChange={(e) => handleNestedChange('socialLinks', 'twitter', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Instagram
              </label>
              <input
                type="url"
                value={formData.socialLinks?.instagram || ''}
                onChange={(e) => handleNestedChange('socialLinks', 'instagram', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
        )}

        {activeTab === 'seo' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Meta Description
              </label>
              <textarea
                value={formData.seo?.metaDescription || ''}
                onChange={(e) => handleNestedChange('seo', 'metaDescription', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                rows="3"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Keywords (comma separated)
              </label>
              <input
                type="text"
                value={formData.seo?.keywords?.join(', ') || ''}
                onChange={(e) => handleNestedChange('seo', 'keywords', e.target.value.split(',').map(k => k.trim()))}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Google Analytics ID
              </label>
              <input
                type="text"
                value={formData.seo?.googleAnalyticsId || ''}
                onChange={(e) => handleNestedChange('seo', 'googleAnalyticsId', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
        )}

        {activeTab === 'about' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Short Bio
              </label>
              <textarea
                value={formData.about?.shortBio || ''}
                onChange={(e) => handleNestedChange('about', 'shortBio', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                rows="3"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Full Bio
              </label>
              <textarea
                value={formData.about?.fullBio || ''}
                onChange={(e) => handleNestedChange('about', 'fullBio', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                rows="6"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Resume
              </label>
              <input
                type="file"
                onChange={(e) => handleFileChange('resume', e.target.files[0])}
                className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                accept=".pdf,.doc,.docx"
              />
              {formData.about?.resume && (
                <a
                  href={formData.about.resume}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-2 inline-flex items-center text-sm text-indigo-600 hover:text-indigo-500"
                >
                  View current resume
                </a>
              )}
            </div>
          </div>
        )}

        <div className="flex justify-end pt-5">
          <button
            type="submit"
            disabled={saving}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {saving ? (
              <>
                <FaSpinner className="animate-spin -ml-1 mr-2 h-5 w-5" />
                Saving...
              </>
            ) : (
              <>
                <FaSave className="-ml-1 mr-2 h-5 w-5" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
