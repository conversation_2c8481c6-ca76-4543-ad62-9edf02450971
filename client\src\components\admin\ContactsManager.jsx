import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaEnvelope, FaCheck, FaTrash, FaExclamation, FaReply } from 'react-icons/fa';
import { contactService } from '../../services/api';
import DeleteConfirmation from './DeleteConfirmation';
import ReplyModal from './ReplyModal';
import SEO from '../SEO';

export default function ContactsManager() {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedContact, setSelectedContact] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [replies, setReplies] = useState([]);
  const [filter, setFilter] = useState('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchContacts();
  }, [filter, pagination.page]);

  const fetchContacts = async () => {
    try {
      const status = filter === 'all' ? '' : filter;
      const response = await contactService.getAllContacts(
        pagination.page,
        pagination.limit,
        status
      );
      setContacts(response.contacts || []);
      setPagination(response.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0
      });
    } catch (error) {
      console.error('Error fetching contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (contact) => {
    try {
      await contactService.updateContactStatus(contact._id, contact.status === 'new' ? 'read' : 'new');
      fetchContacts();
    } catch (error) {
      console.error('Error updating contact status:', error);
    }
  };

  const handleDeleteClick = (contact) => {
    setSelectedContact(contact);
    setIsDeleteModalOpen(true);
  };

  const handleReplyClick = async (contact) => {
    setSelectedContact(contact);
    try {
      const replies = await contactService.getReplies(contact._id);
      setReplies(replies);
      setIsReplyModalOpen(true);
    } catch (error) {
      console.error('Error fetching replies:', error);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await contactService.deleteContact(selectedContact._id);
      fetchContacts();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting contact:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleReply = async (content) => {
    setIsReplying(true);
    try {
      await contactService.replyToContact(selectedContact._id, content);
      fetchContacts();
      setIsReplyModalOpen(false);
    } catch (error) {
      console.error('Error sending reply:', error);
    } finally {
      setIsReplying(false);
    }
  };

  const filteredContacts = contacts.filter((contact) => {
    if (filter === 'all') return true;
    if (filter === 'unread') return contact.status === 'new';
    return contact.status === 'read';
  });

  return (
    <>
      <SEO title="Manage Messages" />

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Contact Messages
          </h2>
          <div className="flex space-x-2">
            {['all', 'unread', 'read'].map((option) => (
              <button
                key={option}
                onClick={() => setFilter(option)}
                className={`px-4 py-2 rounded-md ${
                  filter === option
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="space-y-4">
            <AnimatePresence>
              {filteredContacts.map((contact) => (
                <motion.div
                  key={contact._id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${
                    contact.status === 'new'
                      ? 'border-l-4 border-indigo-500'
                      : ''
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {contact.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {contact.email}
                      </p>
                      <p className="mt-2 text-gray-700 dark:text-gray-300">
                        {contact.message}
                      </p>
                      <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        {new Date(contact.createdAt).toLocaleDateString()}
                      </p>
                      {contact.replies?.length > 0 && (
                        <p className="mt-1 text-sm text-indigo-600 dark:text-indigo-400">
                          {contact.replies.length} {contact.replies.length === 1 ? 'reply' : 'replies'}
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleReplyClick(contact)}
                        className="p-2 rounded-full bg-indigo-100 text-indigo-600 hover:bg-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400"
                      >
                        <FaReply className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleStatusChange(contact)}
                        className={`p-2 rounded-full ${
                          contact.status === 'new'
                            ? 'bg-indigo-100 text-indigo-600 hover:bg-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400'
                            : 'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400'
                        }`}
                      >
                        {contact.status === 'new' ? (
                          <FaEnvelope className="w-5 h-5" />
                        ) : (
                          <FaCheck className="w-5 h-5" />
                        )}
                      </button>
                      <button
                        onClick={() => handleDeleteClick(contact)}
                        className="p-2 rounded-full bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400"
                      >
                        <FaTrash className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {filteredContacts.length === 0 && (
              <div className="flex flex-col items-center justify-center h-64 space-y-4">
                <FaExclamation className="w-12 h-12 text-gray-400" />
                <p className="text-gray-500 dark:text-gray-400">
                  No messages found
                </p>
              </div>
            )}

            {pagination.totalPages > 1 && (
              <div className="flex justify-center space-x-2 mt-6">
                {Array.from({ length: pagination.totalPages }, (_, i) => (
                  <button
                    key={i + 1}
                    onClick={() =>
                      setPagination((prev) => ({ ...prev, page: i + 1 }))
                    }
                    className={`px-4 py-2 rounded-md ${
                      pagination.page === i + 1
                        ? 'bg-indigo-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {i + 1}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      <DeleteConfirmation
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title="Delete Contact"
        message="Are you sure you want to delete this contact? This action cannot be undone."
        isLoading={isDeleting}
      />

      <ReplyModal
        isOpen={isReplyModalOpen}
        onClose={() => setIsReplyModalOpen(false)}
        onSubmit={handleReply}
        isLoading={isReplying}
        contact={selectedContact}
        replies={replies}
      />
    </>
  );
}
