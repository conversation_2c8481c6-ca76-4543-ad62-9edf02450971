import { useState, useRef, useEffect } from 'react';
import { HexColorPicker } from 'react-colorful';
import { useClickAway } from 'react-use';
import { motion, AnimatePresence } from 'framer-motion';

export default function ColorPicker({ color, onChange, label }) {
  const [isOpen, setIsOpen] = useState(false);
  const popover = useRef();

  const close = () => setIsOpen(false);
  useClickAway(popover, close);

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {label}
      </label>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => setIsOpen(true)}
          className={`w-10 h-10 rounded-lg shadow border border-gray-200 dark:border-gray-700`}
          style={{ backgroundColor: color }}
        />
        <input
          type="text"
          value={color}
          onChange={(e) => onChange(e.target.value)}
          className="block w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        />
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={popover}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.1 }}
            className="absolute z-50 top-full mt-2"
          >
            <div className="p-3 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
              <HexColorPicker color={color} onChange={onChange} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
