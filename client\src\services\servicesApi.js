import api from './api';

const servicesApi = {
  getAllServices: async () => {
    const response = await api.get('/services/admin');
    return response.data;
  },

  getPublicServices: async () => {
    const response = await api.get('/services');
    return response.data;
  },

  getService: async (id) => {
    const response = await api.get(`/services/${id}`);
    return response.data;
  },

  createService: async (serviceData) => {
    const response = await api.post('/services', serviceData);
    return response.data;
  },

  updateService: async (id, serviceData) => {
    const response = await api.put(`/services/${id}`, serviceData);
    return response.data;
  },

  deleteService: async (id) => {
    const response = await api.delete(`/services/${id}`);
    return response.data;
  },

  reorderServices: async (services) => {
    const response = await api.post('/services/reorder', { services });
    return response.data;
  }
};

export default servicesApi;
