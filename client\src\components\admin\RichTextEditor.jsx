import { forwardRef, useRef, useImperativeHandle, useState, useEffect } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

// Modern configuration for ReactQuill to avoid findDOMNode warnings
// and DOMNodeInserted mutation event warnings

const modules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ color: [] }, { background: [] }],
    ['link', 'image', 'code-block'],
    ['clean'],
  ],
};

const RichTextEditor = forwardRef(({ value, onChange, className = '' }, ref) => {
  const quillRef = useRef();
  const containerRef = useRef(null);
  const [editorReady, setEditorReady] = useState(false);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    getEditor: () => quillRef.current?.getEditor(),
    focus: () => quillRef.current?.focus(),
    getContainerRef: () => containerRef.current
  }));

  // Wait for component to mount before rendering ReactQuill
  // This helps avoid the findDOMNode warnings
  useEffect(() => {
    setEditorReady(true);
    
    return () => {
      setEditorReady(false);
    };
  }, []);

  // Only render ReactQuill when the container is ready
  // This prevents the findDOMNode warning
  return (
    <div className={`prose prose-sm max-w-none ${className}`} ref={containerRef}>
      {editorReady && (
        <div className="quill-container">
          <ReactQuill
            ref={quillRef}
            value={value}
            onChange={onChange}
            theme="snow"
            modules={modules}
            className="bg-white dark:bg-gray-700 rounded-md [&_.ql-toolbar]:border-gray-300 [&_.ql-toolbar]:dark:border-gray-600 [&_.ql-container]:border-gray-300 [&_.ql-container]:dark:border-gray-600 [&_.ql-editor]:min-h-[200px] [&_.ql-editor]:text-gray-900 [&_.ql-editor]:dark:text-white [&_.ql-toolbar_button]:text-gray-700 [&_.ql-toolbar_button]:dark:text-gray-300 [&_.ql-toolbar_button.ql-active]:text-indigo-600 [&_.ql-toolbar_button.ql-active]:dark:text-indigo-400"
            // Use container class instead of DOM node reference
            bounds=".quill-container"
          />
        </div>
      )}
    </div>
  );
});

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;
