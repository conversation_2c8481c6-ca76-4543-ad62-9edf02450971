import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaComments, FaTimes, FaPaperPlane } from 'react-icons/fa';
import { format } from 'date-fns';
import { io } from 'socket.io-client';
import { useAuth } from '../../contexts/AuthContext';
import { useMedia } from 'react-use';

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [socket, setSocket] = useState(null);
  const [typing, setTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const { user } = useAuth();
  const isMobile = useMedia('(max-width: 640px)');
  const isTablet = useMedia('(max-width: 768px)');

  useEffect(() => {
    if (isOpen) {
      const newSocket = io('https://portfolio-s11i.onrender.com', {
        auth: {
          token: user?.token
        }
      });

      setSocket(newSocket);

      newSocket.on('receive_message', ({ message }) => {
        setMessages(prev => [...prev, message]);
      });

      newSocket.on('user_typing', ({ isTyping }) => {
        setTyping(isTyping);
      });

      return () => newSocket.close();
    }
  }, [isOpen, user]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    if (isOpen) {
      scrollToBottom();
    }
  }, [messages, isOpen]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && socket) {
      socket.emit('send_message', {
        chatId: user?.id,
        message: message.trim()
      });
      setMessage('');
    }
  };

  const handleTyping = (e) => {
    setMessage(e.target.value);
    socket?.emit('typing', {
      chatId: user?.id,
      isTyping: e.target.value.length > 0
    });
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className={`absolute ${isMobile ? 'bottom-16 right-0 left-0 mx-4' : 'bottom-16 right-0'} ${isMobile ? 'w-auto' : isTablet ? 'w-96' : 'w-80'} bg-white dark:bg-gray-800 rounded-lg shadow-xl`}
          >
            <div className="p-4 border-b dark:border-gray-700 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Chat Support</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                <FaTimes />
              </button>
            </div>
            
            <div className="h-96 overflow-y-auto p-4">
              {messages.map((msg, index) => (
                <div
                  key={index}
                  className={`mb-4 ${msg.sender === 'user' ? 'text-right' : 'text-left'}`}
                >
                  <div
                    className={`inline-block p-3 rounded-lg ${
                      msg.sender === 'user'
                        ? 'bg-indigo-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white'
                    }`}
                  >
                    <p className="break-words">{msg.content}</p>
                    <span className="text-xs opacity-70 block mt-1">
                      {format(new Date(msg.timestamp), 'HH:mm')}
                    </span>
                  </div>
                </div>
              ))}
              {typing && (
                <div className="text-left mb-4">
                  <motion.div 
                    className="inline-block p-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white"
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ repeat: Infinity, duration: 1.5 }}
                  >
                    <div className="flex space-x-1">
                      <span className="w-2 h-2 rounded-full bg-gray-500 dark:bg-gray-300 animate-bounce" style={{ animationDelay: '0ms' }}></span>
                      <span className="w-2 h-2 rounded-full bg-gray-500 dark:bg-gray-300 animate-bounce" style={{ animationDelay: '150ms' }}></span>
                      <span className="w-2 h-2 rounded-full bg-gray-500 dark:bg-gray-300 animate-bounce" style={{ animationDelay: '300ms' }}></span>
                    </div>
                  </motion.div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
            
            <form onSubmit={handleSubmit} className="p-4 border-t dark:border-gray-700">
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={message}
                  onChange={handleTyping}
                  placeholder="Type a message..."
                  className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm sm:text-base transition-all duration-200"
                  aria-label="Message input"
                />
                <motion.button
                  type="submit"
                  className={`p-2 rounded-lg transition-colors duration-200 flex items-center justify-center ${!message.trim() ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'} text-white`}
                  whileHover={message.trim() ? { scale: 1.05 } : {}}
                  whileTap={message.trim() ? { scale: 0.95 } : {}}
                  disabled={!message.trim()}
                  aria-label="Send message"
                >
                  <FaPaperPlane className="h-4 w-4 sm:h-5 sm:w-5" />
                </motion.button>
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>
      
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsOpen(!isOpen)}
        className="bg-indigo-600 text-white p-3 sm:p-4 rounded-full shadow-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center justify-center"
        aria-label="Chat support"
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1, type: 'spring', stiffness: 200 }}
      >
        <FaComments className="w-5 h-5 sm:w-6 sm:h-6" />
        {!isOpen && messages.length > 0 && (
          <motion.span 
            className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: 'spring' }}
          >
            {messages.filter(m => m.sender !== 'user').length}
          </motion.span>
        )}
      </motion.button>
    </div>
  );
};

export default ChatWidget;
