import api from './api';

const projectsApi = {
  getAllProjects: async () => {
    const response = await api.get('/projects/admin');
    return response.data;
  },

  getPublicProjects: async () => {
    const response = await api.get('/projects');
    return response.data;
  },

  getFeaturedProjects: async () => {
    const response = await api.get('/projects/featured');
    return response.data;
  },

  getProject: async (id) => {
    const response = await api.get(`/projects/${id}`);
    return response.data;
  },

  createProject: async (projectData) => {
    const response = await api.post('/projects', projectData);
    return response.data;
  },

  updateProject: async (id, projectData) => {
    const response = await api.put(`/projects/${id}`, projectData);
    return response.data;
  },

  deleteProject: async (id) => {
    const response = await api.delete(`/projects/${id}`);
    return response.data;
  },

  reorderProjects: async (projects) => {
    const response = await api.post('/projects/reorder', { projects });
    return response.data;
  }
};

export default projectsApi;
