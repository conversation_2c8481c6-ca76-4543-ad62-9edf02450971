import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const SettingsContext = createContext();

export const useSettings = () => useContext(SettingsContext);

export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/settings');
      setSettings(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (data, files) => {
    try {
      setLoading(true);
      
      // If we have files, use FormData
      if (files && Object.keys(files).some(key => files[key])) {
        const formData = new FormData();
        
        // Add all normal fields as JSON
        formData.append('data', JSON.stringify(data));
        
        // Add all files
        Object.keys(files).forEach(key => {
          if (files[key]) {
            formData.append(key, files[key]);
          }
        });
        
        const response = await axios.patch('/api/settings', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        setSettings(response.data);
      } else {
        // No files, just send JSON
        const response = await axios.patch('/api/settings', data);
        setSettings(response.data);
      }
      
      setError(null);
      return true;
    } catch (err) {
      console.error('Error updating settings:', err);
      setError('Failed to update settings');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  return (
    <SettingsContext.Provider value={{ 
      settings, 
      loading, 
      error, 
      fetchSettings, 
      updateSettings 
    }}>
      {children}
    </SettingsContext.Provider>
  );
}; 