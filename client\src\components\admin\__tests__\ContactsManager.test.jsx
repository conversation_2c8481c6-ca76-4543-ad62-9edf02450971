import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ContactsManager from '../ContactsManager';
import { contactService } from '../../../services/contactService';

jest.mock('../../../services/contactService');

describe('ContactsManager', () => {
  const mockContacts = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      message: 'Test message',
      status: 'unread'
    }
  ];

  beforeEach(() => {
    contactService.getAllContacts.mockResolvedValue(mockContacts);
  });

  test('renders contacts list', async () => {
    render(<ContactsManager />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  test('filters contacts', async () => {
    render(<ContactsManager />);
    
    await waitFor(() => {
      const filterSelect = screen.getByLabelText('Filter messages');
      fireEvent.change(filterSelect, { target: { value: 'unread' } });
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });

  test('marks contact as read', async () => {
    const updateMock = jest.fn();
    contactService.updateContactStatus = updateMock;

    render(<ContactsManager />);
    
    await waitFor(() => {
      const statusButton = screen.getByLabelText('Mark as read');
      fireEvent.click(statusButton);
      expect(updateMock).toHaveBeenCalledWith(1, 'read');
    });
  });

  test('deletes contact', async () => {
    const deleteMock = jest.fn();
    contactService.deleteContact = deleteMock;

    render(<ContactsManager />);
    
    await waitFor(() => {
      const deleteButton = screen.getByLabelText('Delete message');
      fireEvent.click(deleteButton);
      const confirmButton = screen.getByText('Confirm Delete');
      fireEvent.click(confirmButton);
      expect(deleteMock).toHaveBeenCalledWith(1);
    });
  });
});
