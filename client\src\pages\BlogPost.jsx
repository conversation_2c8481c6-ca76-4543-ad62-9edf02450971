import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { FaClock, FaTags } from 'react-icons/fa';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const API_URL = 'https://portfolio-s11i.onrender.com';

const getImageUrl = (path) => {
  // Handle null, undefined or non-string values
  if (!path) return '/vite.svg';
  
  // Handle the case where path is an object with url property (coverImage structure)
  if (typeof path === 'object' && path.url) {
    path = path.url;
  }
  
  // If path is still not a string after handling object case, return default image
  if (typeof path !== 'string') return '/vite.svg';
  
  // If it's already a full URL, return it as is
  if (path.startsWith('http')) return path;
  
  // Fix path if it contains '/images/' incorrectly
  if (path.includes('/uploads/images/')) {
    path = path.replace('/uploads/images/', '/uploads/');
  }
  
  // If the path already includes the API_URL, don't add it again
  if (path.includes(API_URL)) {
    return path;
  }
  
  // Ensure proper path formatting with leading slash
  return `${API_URL}${path.startsWith('/') ? '' : '/'}${path}`;
};

export default function BlogPost() {
  const { slug } = useParams();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPost = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_URL}/api/blogs/slug/${slug}`);
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Blog post not found');
          }
          throw new Error('Failed to fetch blog post');
        }
        const data = await response.json();
        console.log('Fetched post:', data); // Debug log
        setPost(data);
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
        <ErrorMessage message={error} />
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Blog post not found
            </h1>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full mt-12 bg-gray-50 dark:bg-gray-900 py-16">
      <article className="w-full px-4 sm:px-6 lg:px-8">
        <div className="mx-auto lg:max-w-5xl xl:max-w-6xl">
          {post.coverImage && (
            <img
              src={getImageUrl(post.coverImage)}
              alt={post.title}
              className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg mb-8"
              onError={(e) => {
                e.target.src = '/vite.svg';
              }}
            />
          )}

          {post.video && post.video.url && (
            <div className="mb-8">
              <video 
                src={getImageUrl(post.video.url)}
                controls
                className="w-full rounded-lg shadow-lg"
                title={post.video.title || post.title}
              />
              {post.video.title && (
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mt-2">
                  {post.video.title}
                </h3>
              )}
              {post.video.description && (
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {post.video.description}
                </p>
              )}
            </div>
          )}

          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {post.title}
          </h1>

          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-6">
            <div className="flex items-center gap-2">
              <FaClock className="w-4 h-4" />
              <span>
                {format(new Date(post.createdAt || post.publishedAt || Date.now()), 'MMM d, yyyy')}
              </span>
            </div>
            <span>By {post.author || 'Anonymous'}</span>
          </div>

          {post.tags && post.tags.length > 0 && (
            <div className="flex items-center gap-2 mb-8">
              <FaTags className="w-4 h-4 text-gray-400" />
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          <div 
            className="prose prose-lg dark:prose-invert max-w-none"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </div>
      </article>
    </div>
  );
}
