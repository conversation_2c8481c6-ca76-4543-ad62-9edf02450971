import { useState, useEffect } from 'react';
import { Routes, Route, Link, useLocation, Navigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaProjectDiagram, 
  FaComments, 
  FaEnvelope, 
  FaCog, 
  FaSignOutAlt, 
  FaEdit, 
  FaHome,
  FaBlog,
  FaUsers,
  FaImages,
  FaChartLine
} from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import ProjectsManager from '../components/admin/ProjectsManager';
import TestimonialsManager from '../components/admin/TestimonialsManager';
import ContactsManager from '../components/admin/ContactsManager';
import SettingsForm from '../components/admin/SettingsForm';
import ServicesManager from '../components/admin/ServicesManager';
import BlogsManager from '../components/admin/BlogsManager';
import ChatManager from '../components/admin/ChatManager';
import AdminDashboard from '../components/admin/AdminDashboard';
import SEO from '../components/SEO';

export default function Admin() {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [unreadMessages, setUnreadMessages] = useState(0);

  useEffect(() => {
    setLoading(false);
  }, []);

  const navigation = [
    { name: 'Dashboard', href: '', icon: FaHome },
    { name: 'Projects', href: 'projects', icon: FaProjectDiagram },
    { name: 'Blog Posts', href: 'blogs', icon: FaBlog },
    { name: 'Services', href: 'services', icon: FaCog },
    { name: 'Testimonials', href: 'testimonials', icon: FaComments },
    { name: 'Messages', href: 'messages', icon: FaEnvelope, badge: unreadMessages },
    { name: 'Chat', href: 'chat', icon: FaComments },
    { name: 'Settings', href: 'settings', icon: FaCog },
  ];

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  if (!user?.isAdmin) {
    return <Navigate to="/login" replace />;
  }

  return (
    <>
      <SEO
        title="Admin Dashboard"
        description="Manage your portfolio content and settings"
      />

      <div className="min-h-screen mt-24 bg-gray-100 dark:bg-gray-900">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white dark:bg-gray-800 shadow-lg">
            <div className="h-full px-3 py-4 overflow-y-auto">
              <div className="mb-4 p-4 border-b dark:border-gray-700">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Logged in as:</p>
                <p className="text-sm text-gray-900 dark:text-white">{user?.email}</p>
              </div>
              <ul className="space-y-2">
                {navigation.map((item) => {
                  const isActive = location.pathname === `/admin/${item.href}`;
                  return (
                    <li key={item.name}>
                      <Link
                        to={item.href}
                        className={`flex items-center justify-between p-2 text-base font-normal rounded-lg transition-colors ${
                          isActive
                            ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300'
                            : 'text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700'
                        }`}
                      >
                        <div className="flex items-center">
                          <item.icon className={`w-6 h-6 transition-colors ${
                            isActive ? 'text-indigo-700 dark:text-indigo-300' : 'text-gray-500 dark:text-gray-400'
                          }`} />
                          <span className="ml-3">{item.name}</span>
                        </div>
                        {item.badge > 0 && (
                          <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
                            {item.badge}
                          </span>
                        )}
                      </Link>
                    </li>
                  );
                })}
                <li>
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full p-2 text-base font-normal text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <FaSignOutAlt className="w-6 h-6 text-gray-500 dark:text-gray-400" />
                    <span className="ml-3">Logout</span>
                  </button>
                </li>
              </ul>
            </div>
          </div>

          {/* Main content */}
          <div className="flex-1 p-8">
            <Routes>
              <Route index element={<AdminDashboard />} />
              <Route path="projects" element={<ProjectsManager />} />
              <Route path="blogs" element={<BlogsManager />} />
              <Route path="services" element={<ServicesManager />} />
              <Route path="testimonials" element={<TestimonialsManager />} />
              <Route path="messages" element={<ContactsManager />} />
              <Route path="chat" element={<ChatManager />} />
              <Route path="settings" element={<SettingsForm />} />
              <Route path="*" element={<Navigate to="/admin" replace />} />
            </Routes>
          </div>
        </div>
      </div>
    </>
  );
}
