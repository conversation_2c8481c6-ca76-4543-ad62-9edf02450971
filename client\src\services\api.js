import axios from 'axios';

// Create an axios instance with the base URL for all API requests
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'https://portfolio-s11i.onrender.com/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to include the auth token if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export default api;

// API services
export const authService = {
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response.data;
  },
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },
};

export const projectService = {
  getAllProjects: async () => {
    const response = await api.get('/projects');
    return response.data;
  },
  getFeaturedProjects: async () => {
    const response = await api.get('/projects/featured');
    return response.data;
  },
  getProject: async (id) => {
    const response = await api.get(`/projects/${id}`);
    return response.data;
  },
  createProject: async (projectData) => {
    const response = await api.post('/projects', projectData);
    return response.data;
  },
  updateProject: async (id, projectData) => {
    const response = await api.patch(`/projects/${id}`, projectData);
    return response.data;
  },
  deleteProject: async (id) => {
    const response = await api.delete(`/projects/${id}`);
    return response.data;
  },
};

export const testimonialService = {
  getAllTestimonials: async () => {
    const response = await api.get('/testimonials');
    return response.data;
  },
  getTestimonial: async (id) => {
    const response = await api.get(`/testimonials/${id}`);
    return response.data;
  },
  createTestimonial: async (testimonialData) => {
    const response = await api.post('/testimonials', testimonialData);
    return response.data;
  },
  updateTestimonial: async (id, testimonialData) => {
    const response = await api.patch(`/testimonials/${id}`, testimonialData);
    return response.data;
  },
  deleteTestimonial: async (id) => {
    const response = await api.delete(`/testimonials/${id}`);
    return response.data;
  },
};

export const contactService = {
  submitContact: async (contactData) => {
    const response = await api.post('/contact', contactData);
    return response.data;
  },
  getAllContacts: async (page = 1, limit = 10, status = '') => {
    const params = new URLSearchParams({
      page,
      limit,
      ...(status && { status })
    });
    const response = await api.get(`/contact?${params}`);
    return response.data;
  },
  updateContactStatus: async (id, status) => {
    const response = await api.patch(`/contact/${id}/status`, { status });
    return response.data;
  },
  deleteContact: async (id) => {
    const response = await api.delete(`/contact/${id}`);
    return response.data;
  },
  replyToContact: async (id, content) => {
    const response = await api.post(`/contact/${id}/reply`, { content });
    return response.data;
  },
  getReplies: async (id) => {
    const response = await api.get(`/contact/${id}/replies`);
    return response.data;
  }
};

export const blogService = {
  getAllBlogs: async (page = 1, limit = 10, category = '', tag = '') => {
    const params = new URLSearchParams({
      page,
      limit,
      ...(category && { category }),
      ...(tag && { tag }),
    });
    const response = await api.get(`/blogs?${params}`);
    return response.data;
  },

  getBlogBySlug: async (slug) => {
    const response = await api.get(`/blogs/${slug}`);
    return response.data;
  },

  createBlog: async (blogData) => {
    const response = await api.post('/blogs', blogData);
    return response.data;
  },

  updateBlog: async (id, blogData) => {
    const response = await api.patch(`/blogs/${id}`, blogData);
    return response.data;
  },

  deleteBlog: async (id) => {
    const response = await api.delete(`/blogs/${id}`);
    return response.data;
  },

  addComment: async (blogId, commentData) => {
    const response = await api.post(`/blogs/${blogId}/comments`, commentData);
    return response.data;
  },
};

export const settingsService = {
  getSettings: async () => {
    const response = await api.get('/settings');
    return response.data;
  },
  updateSettings: async (settingsData) => {
    const response = await api.patch('/settings', settingsData);
    return response.data;
  },
  uploadLogo: async (file) => {
    const formData = new FormData();
    formData.append('logo', file);
    const response = await api.post('/settings/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  uploadFavicon: async (file) => {
    const formData = new FormData();
    formData.append('favicon', file);
    const response = await api.post('/settings/favicon', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};
