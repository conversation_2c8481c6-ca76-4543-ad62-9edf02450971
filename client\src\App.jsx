import { BrowserRouter as Router, Routes, Route, createRoutesFromElements } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { AccessibilityProvider } from './contexts/AccessibilityContext';
import { SettingsProvider } from './contexts/SettingsContext';
import Navbar from './components/layout/Navbar';
import Home from './pages/Home';
import About from './pages/About';
import Contact from './pages/Contact';
import Footer from './components/layout/Footer';
import Projects from './pages/Projects';
import ProjectDetail from './pages/ProjectDetail';
import Admin from './pages/Admin';
import Login from './pages/Login';
import Blog from './pages/Blog';
import BlogPost from './pages/BlogPost';
import Services from './pages/Services';
import NotFound from './pages/NotFound';
import Testimonials from './pages/Testimonials';
import ProtectedRoute from './components/ProtectedRoute';
import AccessibilityMenu from './components/AccessibilityMenu';
import ThemeToggle from './components/ThemeToggle';
import ChatWidget from './components/chat/ChatWidget';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

function App() {
  return (
    <HelmetProvider>
      <SettingsProvider>
        <AccessibilityProvider>
          <ThemeProvider>
            <AuthProvider>
              <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
                <div className="flex flex-col w-full min-h-screen bg-white dark:bg-gray-900 transition-colors duration-200">
                  <Navbar />
                  <main className="flex-grow">
                    <Routes>
                      <Route path="/" element={<Home />} />
                      <Route path="/about" element={<About />} />
                      <Route path="/projects" element={<Projects />} />
                      <Route path="/projects/:id" element={<ProjectDetail />} />
                      <Route path="/services" element={<Services />} />
                      <Route path="/blog" element={<Blog />} />
                      <Route path="/blog/:slug" element={<BlogPost />} />
                      <Route path="/contact" element={<Contact />} />
                      <Route path="/login" element={<Login />} />
                      <Route
                        path="/admin/*"
                        element={
                          <ProtectedRoute>
                            <Admin />
                          </ProtectedRoute>
                        }
                      />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </main>
                  <Footer />
                  <AccessibilityMenu />
                  <ThemeToggle />
                  <ChatWidget />
                  <ToastContainer position="bottom-right" theme="colored" />
                </div>
              </Router>
            </AuthProvider>
          </ThemeProvider>
        </AccessibilityProvider>
      </SettingsProvider>
    </HelmetProvider>
  );
}

export default App;
