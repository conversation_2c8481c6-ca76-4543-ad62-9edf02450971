import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
// import { motion } from 'framer-motion';
import { FaArrowLeft, FaGithub, FaExternalLinkAlt } from 'react-icons/fa';
import projectsApi from '../services/projectsApi';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import SEO from '../components/SEO';

export default function ProjectDetail() {
  const { id } = useParams();
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeImage, setActiveImage] = useState(0);
  
  // Helper function to get image URL
  const getImageUrl = (image) => {
    if (!image) return '/placeholder-project.svg';
    // If URL starts with http, it's already an absolute URL
    if (image.url.startsWith('http')) return image.url;
    
    // For relative URLs, we need to construct the full URL
    // The API URL from env is 'http://localhost:5001/api', but uploads are served from '/uploads'
    // So we need to remove '/api' and add '/uploads' to get the correct path
    const baseUrl = import.meta.env.VITE_API_URL || 'https://portfolio-s11i.onrender.com/api';
    const serverUrl = baseUrl.endsWith('/api') ? baseUrl.slice(0, -4) : baseUrl;
    
    // If the image URL already starts with '/uploads', use it as is
    // Otherwise, prepend '/uploads' to the URL
    const imagePath = image.url.startsWith('/uploads') ? image.url : `/uploads${image.url}`;
    
    return `${serverUrl}${imagePath}`;
  };

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true);
        const data = await projectsApi.getProject(id);
        setProject(data);
        setError(null);
      } catch (err) {
        console.error('Error loading project:', err);
        setError('Failed to load project details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProject();
    }
  }, [id]);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error || !project) {
    return <ErrorMessage message={error || 'Project not found'} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-16 mt-12">
      <SEO title={project.title} description={project.description} />
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <Link 
          to="/projects" 
          className="inline-flex items-center text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 mb-8"
        >
          <FaArrowLeft className="mr-2" />
          Back to Projects
        </Link>

        {/* Project Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {project.title}
          </h1>
          
          <div className="flex flex-wrap gap-2 mb-6">
            {project.technologies && project.technologies.map((tech, index) => (
              <span 
                key={index}
                className="px-3 py-1 text-sm bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-300 rounded-full"
              >
                {tech}
              </span>
            ))}
          </div>
          
          <div className="flex flex-wrap gap-4">
            {project.githubUrl && (
              <a 
                href={project.githubUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-gray-800 dark:bg-gray-700 text-white rounded-lg hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors"
              >
                <FaGithub className="mr-2" />
                View Code
              </a>
            )}
            {project.liveUrl && (
              <a 
                href={project.liveUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                <FaExternalLinkAlt className="mr-2" />
                Live Demo
              </a>
            )}
          </div>
        </div>

        {/* Project Content */}
        <div className="grid grid-cols-1 gap-12">
          {/* Main Content - Images */}
          <div>
            {project.images && project.images.length > 0 ? (
              <div>
                {/* Main Image */}
                <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg mb-4">
                  <img 
                    src={getImageUrl(project.images[activeImage])} 
                    alt={project.images[activeImage].alt || project.title}
                    className="w-full h-auto object-cover"
                  />
                </div>
                
                {/* Image Thumbnails */}
                {project.images.length > 1 && (
                  <div className="flex gap-2 overflow-x-auto pb-2">
                    {project.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveImage(index)}
                        className={`relative rounded-lg overflow-hidden flex-shrink-0 w-20 h-20 border-2 ${activeImage === index ? 'border-indigo-600 dark:border-indigo-400' : 'border-transparent'}`}
                      >
                        <img 
                          src={getImageUrl(image)} 
                          alt={image.alt || `${project.title} thumbnail ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg flex items-center justify-center h-64">
                <p className="text-gray-500 dark:text-gray-400">No images available</p>
              </div>
            )}

            {/* Project Videos */}
            {project.videos && project.videos.length > 0 && (
              <div className="mt-8">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Project Videos
                </h3>
                <div className="space-y-6">
                  {project.videos.map((video, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg p-4">
                      <video 
                        src={video.url} 
                        controls 
                        className="w-full rounded-lg"
                        poster={project.images && project.images[0] ? getImageUrl(project.images[0]) : undefined}
                      />
                      {video.title && (
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white mt-4">
                          {video.title}
                        </h4>
                      )}
                      {video.description && (
                        <p className="text-gray-600 dark:text-gray-300 mt-2">
                          {video.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Description Section */}
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                About this Project
              </h2>
              <div className="prose dark:prose-invert max-w-none">
                <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                  {project.description}
                </p>
              </div>
              
              {project.category && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Category
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {project.category}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}