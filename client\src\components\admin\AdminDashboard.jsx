import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaProjectDiagram, 
  FaComments, 
  FaEnvelope, 
  FaCog, 
  FaEdit, 
  FaBlog,
  FaChartLine
} from 'react-icons/fa';

const DashboardCard = ({ title, icon: Icon, description, to, color }) => (
  <Link to={to}>
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`p-6 rounded-lg shadow-lg ${color} hover:shadow-xl transition-all duration-200`}
    >
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-white mb-2">{title}</h3>
          <p className="text-white/80 text-sm">{description}</p>
        </div>
        <Icon className="w-8 h-8 text-white/90" />
      </div>
    </motion.div>
  </Link>
);

const AdminDashboard = () => {
  const cards = [
    {
      title: 'Projects',
      icon: FaProjectDiagram,
      description: 'Manage your portfolio projects',
      to: 'projects',
      color: 'bg-blue-600'
    },
    {
      title: 'Blog Posts',
      icon: FaBlog,
      description: 'Write and edit blog content',
      to: 'blogs',
      color: 'bg-purple-600'
    },
    {
      title: 'Services',
      icon: FaCog,
      description: 'Update your service offerings',
      to: 'services',
      color: 'bg-green-600'
    },
    {
      title: 'Testimonials',
      icon: FaComments,
      description: 'Manage client testimonials',
      to: 'testimonials',
      color: 'bg-yellow-600'
    },
    {
      title: 'Messages',
      icon: FaEnvelope,
      description: 'View contact form submissions',
      to: 'messages',
      color: 'bg-red-600'
    },
    {
      title: 'Settings',
      icon: FaCog,
      description: 'Configure site settings',
      to: 'settings',
      color: 'bg-gray-600'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">Welcome to Admin Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-300">Manage your portfolio content and settings from here.</p>
      </div>

      {/* Quick Stats */}
      <div className="mb-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="flex items-center space-x-4">
          <FaChartLine className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Quick Overview</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
          <div className="p-4 bg-indigo-50 dark:bg-indigo-900/30 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">Total Projects</p>
            <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">12</p>
          </div>
          <div className="p-4 bg-green-50 dark:bg-green-900/30 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">Services</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">6</p>
          </div>
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">Testimonials</p>
            <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">8</p>
          </div>
          <div className="p-4 bg-red-50 dark:bg-red-900/30 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">New Messages</p>
            <p className="text-2xl font-bold text-red-600 dark:text-red-400">3</p>
          </div>
        </div>
      </div>

      {/* Navigation Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {cards.map((card) => (
          <DashboardCard key={card.title} {...card} />
        ))}
      </div>
    </div>
  );
};

export default AdminDashboard;
