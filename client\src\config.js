// Environment configuration with validation
const requiredEnvVars = {
  VITE_API_URL: import.meta.env.VITE_API_URL,
  VITE_NODE_ENV: import.meta.env.VITE_NODE_ENV,
};

// Validate required environment variables
const missingVars = Object.entries(requiredEnvVars)
  .filter(([key, value]) => !value)
  .map(([key]) => key);

if (missingVars.length > 0 && import.meta.env.MODE === 'production') {
  console.warn('Missing environment variables:', missingVars);
}

// Configuration object
export const config = {
  API_URL: import.meta.env.VITE_API_URL || 'https://portfolio-s11i.onrender.com/api',
  NODE_ENV: import.meta.env.VITE_NODE_ENV || 'development',
  IS_PRODUCTION: import.meta.env.MODE === 'production',
  IS_DEVELOPMENT: import.meta.env.MODE === 'development',
};

// Legacy export for backward compatibility
export const API_URL = config.API_URL;

// Helper functions
export const getImageUrl = (imagePath, baseUrl = config.API_URL) => {
  if (!imagePath) return '/placeholder-project.svg';

  // Handle object with url property
  if (typeof imagePath === 'object' && imagePath.url) {
    imagePath = imagePath.url;
  }

  // If it's already a full URL, return as is
  if (typeof imagePath === 'string' && imagePath.startsWith('http')) {
    return imagePath;
  }

  // Construct full URL
  const serverUrl = baseUrl.endsWith('/api') ? baseUrl.slice(0, -4) : baseUrl;
  const cleanPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;

  return `${serverUrl}${cleanPath}`;
};

export default config;
