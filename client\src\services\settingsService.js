import api from './api';

export const settingsService = {
  // Get all settings
  getSettings: async () => {
    const response = await api.get('/settings');
    return response.data;
  },

  // Update all settings
  updateSettings: async (settingsData, files = null) => {
    try {
      // If we have files, use FormData
      if (files && Object.keys(files).some(key => files[key])) {
        const formData = new FormData();
        
        // Handle nested objects by stringifying them
        Object.keys(settingsData).forEach(key => {
          const value = settingsData[key];
          
          if (value === null || value === undefined) {
            return; // Skip null/undefined values
          }
          
          if (typeof value === 'object' && !(value instanceof File)) {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value);
          }
        });
        
        // Add files
        Object.keys(files).forEach(key => {
          if (files[key]) {
            formData.append(key, files[key]);
          }
        });
        
        const response = await api.patch('/settings', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        
        return response.data;
      } else {
        // No files, just send JSON
        const response = await api.patch('/settings', settingsData);
        return response.data;
      }
    } catch (error) {
      console.error('Settings service error:', error);
      throw error;
    }
  },

  // Update specific section
  updateSection: async (section, data, files = null) => {
    let formData = null;

    if (files || typeof data === 'object') {
      formData = new FormData();

      if (files) {
        Object.keys(files).forEach(key => {
          if (files[key]) {
            formData.append(key, files[key]);
          }
        });
      }

      if (typeof data === 'object') {
        Object.keys(data).forEach(key => {
          if (typeof data[key] === 'object' && !(data[key] instanceof File)) {
            formData.append(key, JSON.stringify(data[key]));
          } else {
            formData.append(key, data[key]);
          }
        });
      }

      const response = await api.patch(`/settings/${section}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    }

    const response = await api.patch(`/settings/${section}`, data);
    return response.data;
  },

  // File upload helpers
  uploadFile: async (section, field, file, metadata = {}) => {
    const formData = new FormData();
    formData.append('file', file);
    
    if (Object.keys(metadata).length > 0) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    const response = await api.post(`/settings/${section}/upload/${field}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Specific upload methods
  uploadLogo: async (file, alt = '') => {
    return settingsService.uploadFile('basic', 'logo', file, { alt });
  },

  uploadFavicon: async (file) => {
    return settingsService.uploadFile('basic', 'favicon', file);
  },

  uploadHeroImage: async (file, alt = '') => {
    return settingsService.uploadFile('hero', 'image', file, { alt });
  },

  uploadAboutImage: async (file, alt = '') => {
    return settingsService.uploadFile('about', 'image', file, { alt });
  },

  uploadResume: async (file) => {
    return settingsService.uploadFile('about', 'resume', file);
  }
};
