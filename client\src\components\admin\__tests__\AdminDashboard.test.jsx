import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import AdminDashboard from '../AdminDashboard';

describe('AdminDashboard', () => {
  const renderDashboard = () => {
    render(
      <BrowserRouter>
        <AdminDashboard />
      </BrowserRouter>
    );
  };

  test('renders welcome message', () => {
    renderDashboard();
    expect(screen.getByText('Welcome to Admin Dashboard')).toBeInTheDocument();
  });

  test('renders all dashboard cards', () => {
    renderDashboard();
    expect(screen.getByText('Projects')).toBeInTheDocument();
    expect(screen.getByText('Blog Posts')).toBeInTheDocument();
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.getByText('Testimonials')).toBeInTheDocument();
    expect(screen.getByText('Messages')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  test('renders quick overview section', () => {
    renderDashboard();
    expect(screen.getByText('Quick Overview')).toBeInTheDocument();
    expect(screen.getByText('Total Projects')).toBeInTheDocument();
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.getByText('Testimonials')).toBeInTheDocument();
  });
});
