import api from './api';

const blogApi = {
  getAllPosts: async () => {
    const response = await api.get('/blog/admin');
    return response.data;
  },

  getPublicPosts: async () => {
    const response = await api.get('/blog');
    return response.data;
  },

  getFeaturedPosts: async () => {
    const response = await api.get('/blog/featured');
    return response.data;
  },

  getPost: async (slug) => {
    const response = await api.get(`/blog/${slug}`);
    return response.data;
  },

  createPost: async (postData) => {
    const formData = new FormData();
    
    // Handle file upload
    if (postData.coverImage) {
      formData.append('coverImage', postData.coverImage);
      delete postData.coverImage;
    }
    
    // Append other data
    Object.keys(postData).forEach(key => {
      if (Array.isArray(postData[key])) {
        formData.append(key, JSON.stringify(postData[key]));
      } else {
        formData.append(key, postData[key]);
      }
    });

    const response = await api.post('/blog', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  updatePost: async (slug, postData) => {
    const formData = new FormData();
    
    // Handle file upload
    if (postData.coverImage) {
      formData.append('coverImage', postData.coverImage);
      delete postData.coverImage;
    }
    
    // Append other data
    Object.keys(postData).forEach(key => {
      if (Array.isArray(postData[key])) {
        formData.append(key, JSON.stringify(postData[key]));
      } else {
        formData.append(key, postData[key]);
      }
    });

    const response = await api.put(`/blog/${slug}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  deletePost: async (slug) => {
    const response = await api.delete(`/blog/${slug}`);
    return response.data;
  },

  // Categories
  getCategories: async () => {
    const response = await api.get('/blog/categories');
    return response.data;
  },

  // Tags
  getTags: async () => {
    const response = await api.get('/blog/tags');
    return response.data;
  },

  // Search
  searchPosts: async (query) => {
    const response = await api.get(`/blog/search?q=${encodeURIComponent(query)}`);
    return response.data;
  },

  // Filter by category
  getPostsByCategory: async (category) => {
    const response = await api.get(`/blog/category/${encodeURIComponent(category)}`);
    return response.data;
  },

  // Filter by tag
  getPostsByTag: async (tag) => {
    const response = await api.get(`/blog/tag/${encodeURIComponent(tag)}`);
    return response.data;
  }
};

export default blogApi;
