import { Helmet } from 'react-helmet-async';

export default function SEO({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  author = 'Bosona Gosaye',
}) {
  const siteTitle = 'Bosona Gosaye - Full-Stack Developer & UX/UI Designer';
  const defaultDescription = 'Professional portfolio of Bosona Gosaye - Full-Stack Developer, UX/UI Designer, and Mobile App Developer. Specializing in MERN stack, React, Node.js, and modern web technologies.';
  const siteUrl = 'https://portfolio-s11i.onrender.com'; // Your actual domain
  const defaultKeywords = [
    'Bosona Gosaye',
    'Full-Stack Developer',
    'UX/UI Designer',
    'React Developer',
    'Node.js Developer',
    'MERN Stack',
    'Web Development',
    'Mobile App Development',
    'Frontend Developer',
    'Backend Developer',
    'JavaScript',
    'TypeScript',
    'Portfolio',
    'Ethiopia Developer'
  ];

  const metaDescription = description || defaultDescription;
  const metaImage = image || `${siteUrl}/profile/bosona.jpg`; // Default Open Graph image
  const canonicalUrl = url || siteUrl;
  const allKeywords = [...defaultKeywords, ...keywords];

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title ? `${title} | ${siteTitle}` : siteTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={allKeywords.join(', ')} />
      <meta name="author" content={author} />
      <link rel="canonical" href={canonicalUrl} />

      {/* Additional SEO Meta Tags */}
      <meta name="language" content="en" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />

      {/* Open Graph Meta Tags */}
      <meta property="og:site_name" content={siteTitle} />
      <meta property="og:title" content={title || siteTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={metaImage} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={canonicalUrl} />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title || siteTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={metaImage} />

      {/* Additional Meta Tags */}
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#4F46E5" /> {/* Indigo-600 color */}
    </Helmet>
  );
}
