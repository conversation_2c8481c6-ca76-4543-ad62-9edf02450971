import { Helmet } from 'react-helmet-async';

export default function SEO({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
}) {
  const siteTitle = 'Your Portfolio';
  const defaultDescription = 'Professional portfolio showcasing web development projects and services';
  const siteUrl = 'https://yourportfolio.com'; // Replace with your actual domain

  const metaDescription = description || defaultDescription;
  const metaImage = image || `${siteUrl}/og-image.jpg`; // Default Open Graph image
  const canonicalUrl = url || siteUrl;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title ? `${title} | ${siteTitle}` : siteTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={keywords.join(', ')} />
      <link rel="canonical" href={canonicalUrl} />

      {/* Open Graph Meta Tags */}
      <meta property="og:site_name" content={siteTitle} />
      <meta property="og:title" content={title || siteTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={metaImage} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={canonicalUrl} />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title || siteTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={metaImage} />

      {/* Additional Meta Tags */}
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#4F46E5" /> {/* Indigo-600 color */}
    </Helmet>
  );
}
