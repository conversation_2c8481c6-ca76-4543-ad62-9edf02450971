import { createContext, useContext, useState, useEffect } from 'react';

const AccessibilityContext = createContext();

export function AccessibilityProvider({ children }) {
  const [preferences, setPreferences] = useState(() => {
    const saved = localStorage.getItem('accessibility-preferences');
    return saved ? JSON.parse(saved) : {
      reducedMotion: false,
      highContrast: false,
      largeText: false,
      focusIndicators: true,
    };
  });

  useEffect(() => {
    localStorage.setItem('accessibility-preferences', JSON.stringify(preferences));
    
    // Apply preferences to document
    document.documentElement.classList.toggle('reduce-motion', preferences.reducedMotion);
    document.documentElement.classList.toggle('high-contrast', preferences.highContrast);
    document.documentElement.classList.toggle('large-text', preferences.largeText);
    document.documentElement.classList.toggle('focus-visible', preferences.focusIndicators);
  }, [preferences]);

  const toggleReducedMotion = () => {
    setPreferences(prev => ({
      ...prev,
      reducedMotion: !prev.reducedMotion,
    }));
  };

  const toggleHighContrast = () => {
    setPreferences(prev => ({
      ...prev,
      highContrast: !prev.highContrast,
    }));
  };

  const toggleLargeText = () => {
    setPreferences(prev => ({
      ...prev,
      largeText: !prev.largeText,
    }));
  };

  const toggleFocusIndicators = () => {
    setPreferences(prev => ({
      ...prev,
      focusIndicators: !prev.focusIndicators,
    }));
  };

  return (
    <AccessibilityContext.Provider
      value={{
        ...preferences,
        toggleReducedMotion,
        toggleHighContrast,
        toggleLargeText,
        toggleFocusIndicators,
      }}
    >
      {children}
    </AccessibilityContext.Provider>
  );
}

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (context === undefined) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
}
