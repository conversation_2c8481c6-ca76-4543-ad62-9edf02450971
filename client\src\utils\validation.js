export const validateEmail = (email) => {
  const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return re.test(email);
};

export const validatePassword = (password) => {
  return {
    isValid: password.length >= 8,
    message: password.length < 8 ? 'Password must be at least 8 characters long' : '',
  };
};

export const validateRequired = (value, fieldName) => {
  return {
    isValid: value.trim().length > 0,
    message: value.trim().length === 0 ? `${fieldName} is required` : '',
  };
};

export const validatePhone = (phone) => {
  const re = /^\+?[1-9]\d{1,14}$/;
  return {
    isValid: re.test(phone),
    message: re.test(phone) ? '' : 'Please enter a valid phone number',
  };
};

export const validateProjectForm = (values) => {
  const errors = {};

  if (!values.title) {
    errors.title = 'Title is required';
  }

  if (!values.description) {
    errors.description = 'Description is required';
  }

  if (!values.technologies || values.technologies.length === 0) {
    errors.technologies = 'At least one technology is required';
  }

  if (values.githubUrl && !values.githubUrl.startsWith('https://github.com/')) {
    errors.githubUrl = 'Please enter a valid GitHub URL';
  }

  if (values.liveUrl && !values.liveUrl.startsWith('http')) {
    errors.liveUrl = 'Please enter a valid URL';
  }

  return errors;
};

export const validateTestimonialForm = (values) => {
  const errors = {};

  if (!values.name) {
    errors.name = 'Name is required';
  }

  if (!values.position) {
    errors.position = 'Position is required';
  }

  if (!values.company) {
    errors.company = 'Company is required';
  }

  if (!values.content) {
    errors.content = 'Content is required';
  }

  if (values.rating && (values.rating < 1 || values.rating > 5)) {
    errors.rating = 'Rating must be between 1 and 5';
  }

  return errors;
};

export const validateContactForm = (values) => {
  const errors = {};

  if (!values.name) {
    errors.name = 'Name is required';
  }

  if (!values.email) {
    errors.email = 'Email is required';
  } else if (!validateEmail(values.email)) {
    errors.email = 'Please enter a valid email address';
  }

  if (!values.subject) {
    errors.subject = 'Subject is required';
  }

  if (!values.message) {
    errors.message = 'Message is required';
  } else if (values.message.length < 10) {
    errors.message = 'Message must be at least 10 characters long';
  }

  return errors;
};
