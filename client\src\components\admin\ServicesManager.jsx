import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash } from 'react-icons/fa';
import servicesApi from '../../services/servicesApi';
import Modal from './Modal';
import ServiceForm from './ServiceForm';
import DeleteConfirmation from './DeleteConfirmation';
import LoadingSpinner from '../LoadingSpinner';
import { toast } from 'react-toastify';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

export default function ServicesManager() {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedService, setSelectedService] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      const data = await servicesApi.getAllServices();
      setServices(data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast.error('Failed to load services');
      setLoading(false);
    }
  };

  const handleAddClick = () => {
    setSelectedService(null);
    setModalMode('add');
    setIsModalOpen(true);
  };

  const handleEditClick = (service) => {
    setSelectedService(service);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDeleteClick = (service) => {
    setSelectedService(service);
    setIsDeleteModalOpen(true);
  };

  const handleSubmit = async (formData) => {
    try {
      if (modalMode === 'add') {
        await servicesApi.createService(formData);
        toast.success('Service created successfully');
      } else {
        await servicesApi.updateService(selectedService._id, formData);
        toast.success('Service updated successfully');
      }
      await fetchServices();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving service:', error);
      toast.error('Failed to save service');
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await servicesApi.deleteService(selectedService._id);
      toast.success('Service deleted successfully');
      await fetchServices();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(services);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setServices(items);

    try {
      await servicesApi.reorderServices(
        items.map((item, index) => ({ id: item._id, order: index }))
      );
    } catch (error) {
      console.error('Error reordering services:', error);
      toast.error('Failed to save order');
      await fetchServices();
    }
  };

  if (loading) return <LoadingSpinner />;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Manage Services
        </h2>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleAddClick}
          className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <FaPlus className="w-4 h-4 mr-2" />
          Add Service
        </motion.button>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="services-list" mode="standard" type="DEFAULT">
          {(provided, snapshot) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${
                snapshot.isDraggingOver ? 'bg-gray-50 dark:bg-gray-900/50' : ''
              }`}
            >
              {services.map((service, index) => (
                <Draggable 
                  key={service._id} 
                  draggableId={service._id} 
                  index={index}
                  type="DEFAULT"
                >
                  {(provided, snapshot) => (
                    <motion.div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      style={{
                        ...provided.draggableProps.style,
                        transform: snapshot.isDragging
                          ? provided.draggableProps.style?.transform
                          : 'none'
                      }}
                      layout
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden ${
                        snapshot.isDragging ? 'ring-2 ring-indigo-500 shadow-xl' : ''
                      }`}
                    >
                      <div className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                              {service.title}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {service.description}
                            </p>
                          </div>
                          <div className="flex space-x-2 ml-4">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleEditClick(service)}
                              className="p-2 text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400 transition-colors"
                            >
                              <FaEdit className="w-4 h-4" />
                            </motion.button>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleDeleteClick(service)}
                              className="p-2 text-gray-600 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 transition-colors"
                            >
                              <FaTrash className="w-4 h-4" />
                            </motion.button>
                          </div>
                        </div>
                        {service.icon && (
                          <div className="mt-4">
                            <img
                              src={service.icon}
                              alt={`${service.title} icon`}
                              className="w-12 h-12 object-contain"
                            />
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalMode === 'add' ? 'Add Service' : 'Edit Service'}
      >
        <ServiceForm
          initialData={selectedService}
          onSubmit={handleSubmit}
          onCancel={() => setIsModalOpen(false)}
        />
      </Modal>

      <DeleteConfirmation
        onConfirm={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        itemType="Service"
        isDeleting={isDeleting}
      />
    </div>
  );
}
