const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  // Basic Site Info
  siteTitle: {
    type: String,
    required: true,
    default: 'My Portfolio'
  },
  description: {
    type: String,
    required: true,
    default: 'Welcome to my portfolio website'
  },
  logo: {
    url: String,
    alt: String
  },
  favicon: String,

  // Header/Navigation
  navigation: [{
    label: String,
    path: String,
    order: Number
  }],

  // Hero Section
  hero: {
    title: String,
    subtitle: String,
    buttonText: String,
    buttonLink: String,
    image: {
      url: String,
      alt: String
    }
  },

  // About Section
  about: {
    title: String,
    content: String,
    image: {
      url: String,
      alt: String
    },
    resume: String,
    skills: [{
      name: String,
      level: Number,
      icon: String
    }],
    experience: [{
      title: String,
      company: String,
      period: String,
      description: String
    }],
    education: [{
      degree: String,
      institution: String,
      period: String,
      description: String
    }]
  },

  // Contact Information
  contact: {
    email: {
      type: String,
      default: '<EMAIL>'
    },
    phone: String,
    address: String,
    formEnabled: {
      type: Boolean,
      default: true
    },
    socialLinks: {
      github: String,
      linkedin: String,
      twitter: String,
      instagram: String,
      facebook: String
    }
  },

  // Footer
  footer: {
    copyright: String,
    links: [{
      label: String,
      url: String
    }]
  },

  // Theme/Appearance
  appearance: {
    theme: {
      primary: {
        type: String,
        default: '#4F46E5' // indigo-600
      },
      secondary: {
        type: String,
        default: '#1F2937' // gray-800
      }
    },
    fonts: {
      heading: String,
      body: String
    },
    darkMode: {
      type: Boolean,
      default: true
    }
  },

  // SEO Settings
  seo: {
    metaTitle: String,
    metaDescription: String,
    ogImage: String,
    keywords: [String],
    googleAnalyticsId: String
  },

  // Timestamps
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Settings', settingsSchema);
