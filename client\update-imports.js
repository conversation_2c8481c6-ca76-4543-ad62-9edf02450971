const fs = require('fs');
const path = require('path');

const directoryPath = path.join(__dirname, 'src');
const pattern = /from\s+['"](.+?)\/SettingsContext['"];/g;
const replacement = 'from "$1/SettingsContext.jsx";';

function searchFiles(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      searchFiles(filePath);
    } else if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.jsx'))) {
      const content = fs.readFileSync(filePath, 'utf8');
      const updatedContent = content.replace(pattern, replacement);
      
      if (content !== updatedContent) {
        console.log(`Updating file: ${filePath}`);
        fs.writeFileSync(filePath, updatedContent, 'utf8');
      }
    }
  });
}

searchFiles(directoryPath);
console.log('Import paths updated!'); 