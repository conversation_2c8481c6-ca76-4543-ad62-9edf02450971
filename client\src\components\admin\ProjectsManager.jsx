import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash, FaStar } from 'react-icons/fa';
import projectsApi from '../../services/projectsApi';
import Modal from './Modal';
import ProjectForm from './ProjectForm';
import DeleteConfirmation from './DeleteConfirmation';
import SEO from '../SEO';
import { toast } from 'react-toastify';

export default function ProjectsManager() {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      const data = await projectsApi.getAllProjects();
      setProjects(data);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const handleAddClick = () => {
    setSelectedProject(null);
    setModalMode('add');
    setIsModalOpen(true);
  };

  const handleEditClick = (project) => {
    setSelectedProject(project);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDeleteClick = (project) => {
    setSelectedProject(project);
    setIsDeleteModalOpen(true);
  };

  const handleSubmit = async (formData) => {
    try {
      if (modalMode === 'add') {
        await projectsApi.createProject(formData);
        toast.success('Project created successfully');
      } else {
        await projectsApi.updateProject(selectedProject._id, formData);
        toast.success('Project updated successfully');
      }
      await fetchProjects();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving project:', error);
      toast.error('Failed to save project');
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await projectsApi.deleteProject(selectedProject._id);
      toast.success('Project deleted successfully');
      await fetchProjects();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting project:', error);
      toast.error('Failed to delete project');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleFeatured = async (project) => {
    try {
      await projectsApi.updateProject(project._id, {
        ...project,
        isFeatured: !project.isFeatured,
      });
      toast.success(`Project ${project.isFeatured ? 'removed from' : 'added to'} featured`);
      await fetchProjects();
    } catch (error) {
      console.error('Error updating project:', error);
      toast.error('Failed to update project');
    }
  };

  return (
    <>
      <SEO title="Manage Projects" />

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Manage Projects
          </h2>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAddClick}
            className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FaPlus className="w-4 h-4 mr-2" />
            Add Project
          </motion.button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              <AnimatePresence>
                {projects.map((project) => (
                  <motion.li
                    key={project._id}
                    layout
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <div className="px-6 py-4 flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0 h-12 w-12">
                          <img
                            src={project.image}
                            alt={project.title}
                            className="h-12 w-12 rounded-lg object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                            {project.title}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {project.technologies.join(', ')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => handleToggleFeatured(project)}
                          className={`p-2 rounded-full ${
                            project.isFeatured
                              ? 'text-yellow-500 hover:text-yellow-600'
                              : 'text-gray-400 hover:text-gray-500'
                          }`}
                        >
                          <FaStar className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleEditClick(project)}
                          className="p-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-900"
                        >
                          <FaEdit className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(project)}
                          className="p-2 text-red-600 dark:text-red-400 hover:text-red-900"
                        >
                          <FaTrash className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </motion.li>
                ))}
              </AnimatePresence>
            </ul>
          </div>
        )}
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalMode === 'add' ? 'Add Project' : 'Edit Project'}
      >
        <ProjectForm
          initialData={selectedProject}
          onSubmit={handleSubmit}
          onCancel={() => setIsModalOpen(false)}
        />
      </Modal>

      <DeleteConfirmation
        onConfirm={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        itemType="Project"
        isDeleting={isDeleting}
      />
    </>
  );
}
