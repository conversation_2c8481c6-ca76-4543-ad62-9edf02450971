import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash } from 'react-icons/fa';
import { testimonialService } from '../../services/api';
import Modal from './Modal';
import TestimonialForm from './TestimonialForm';
import DeleteConfirmation from './DeleteConfirmation';
import SEO from '../SEO';

export default function TestimonialsManager() {
  const [testimonials, setTestimonials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTestimonial, setSelectedTestimonial] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    fetchTestimonials();
  }, []);

  const fetchTestimonials = async () => {
    try {
      const data = await testimonialService.getAllTestimonials();
      setTestimonials(data);
    } catch (error) {
      console.error('Error fetching testimonials:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddClick = () => {
    setSelectedTestimonial(null);
    setModalMode('add');
    setIsModalOpen(true);
  };

  const handleEditClick = (testimonial) => {
    setSelectedTestimonial(testimonial);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDeleteClick = (testimonial) => {
    setSelectedTestimonial(testimonial);
    setIsDeleteModalOpen(true);
  };

  const handleSubmit = async (formData) => {
    try {
      if (modalMode === 'add') {
        await testimonialService.createTestimonial(formData);
      } else {
        await testimonialService.updateTestimonial(selectedTestimonial._id, formData);
      }
      fetchTestimonials();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving testimonial:', error);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await testimonialService.deleteTestimonial(selectedTestimonial._id);
      fetchTestimonials();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting testimonial:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <SEO title="Manage Testimonials" />

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Manage Testimonials
          </h2>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAddClick}
            className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FaPlus className="w-4 h-4 mr-2" />
            Add Testimonial
          </motion.button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {testimonials.map((testimonial) => (
                <motion.div
                  key={testimonial._id}
                  layout
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="flex-shrink-0 h-12 w-12">
                        <img
                          src={testimonial.image}
                          alt={testimonial.name}
                          className="h-12 w-12 rounded-full object-cover"
                        />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {testimonial.name}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {testimonial.position} at {testimonial.company}
                        </p>
                      </div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {testimonial.content}
                    </p>
                    <div className="flex justify-end space-x-2">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => handleEditClick(testimonial)}
                        className="p-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-900"
                      >
                        <FaEdit className="w-5 h-5" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => handleDeleteClick(testimonial)}
                        className="p-2 text-red-600 dark:text-red-400 hover:text-red-900"
                      >
                        <FaTrash className="w-5 h-5" />
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalMode === 'add' ? 'Add Testimonial' : 'Edit Testimonial'}
      >
        <TestimonialForm
          initialData={selectedTestimonial}
          onSubmit={handleSubmit}
          onCancel={() => setIsModalOpen(false)}
        />
      </Modal>

      <DeleteConfirmation
        isOpen={isDeleteModalOpen}
        onConfirm={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        itemType="Testimonial"
        isDeleting={isDeleting}
      />
    </>
  );
}
