import React, { createContext, useContext, useState, useEffect } from 'react';
import { settingsService } from '../services/settingsService';
import { toast } from 'react-toastify';

const SettingsContext = createContext(null);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const data = await settingsService.getSettings();
      setSettings(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (data, files) => {
    try {
      setLoading(true);
      
      // Ensure contact has an email field if it exists
      const dataToUpdate = { ...data };
      if (dataToUpdate.contact && !dataToUpdate.contact.email) {
        if (settings?.contact?.email) {
          dataToUpdate.contact.email = settings.contact.email;
        } else {
          dataToUpdate.contact.email = '<EMAIL>';
        }
      }
      
      const updatedSettings = await settingsService.updateSettings(dataToUpdate, files);
      setSettings(updatedSettings);
      setError(null);
      toast.success('Settings updated successfully');
      return true;
    } catch (err) {
      console.error('Error updating settings:', err);
      setError('Failed to update settings');
      toast.error('Failed to update settings');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateSection = async (section, data) => {
    try {
      const updatedSettings = await settingsService.updateSection(section, data);
      setSettings(updatedSettings);
      toast.success(`${section} settings updated successfully`);
      return updatedSettings;
    } catch (error) {
      console.error(`Error updating ${section} settings:`, error);
      toast.error(`Failed to update ${section} settings`);
      throw error;
    }
  };

  const uploadLogo = async (file, alt) => {
    try {
      const updatedSettings = await settingsService.uploadLogo(file, alt);
      setSettings(updatedSettings);
      toast.success('Logo updated successfully');
      return updatedSettings;
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast.error('Failed to upload logo');
      throw error;
    }
  };

  const uploadFavicon = async (file) => {
    try {
      const updatedSettings = await settingsService.uploadFavicon(file);
      setSettings(updatedSettings);
      toast.success('Favicon updated successfully');
      return updatedSettings;
    } catch (error) {
      console.error('Error uploading favicon:', error);
      toast.error('Failed to upload favicon');
      throw error;
    }
  };

  const uploadResume = async (file) => {
    try {
      const updatedSettings = await settingsService.uploadResume(file);
      setSettings(updatedSettings);
      toast.success('Resume uploaded successfully');
      return updatedSettings;
    } catch (error) {
      console.error('Error uploading resume:', error);
      toast.error('Failed to upload resume');
      throw error;
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  return (
    <SettingsContext.Provider
      value={{
        settings,
        loading,
        error,
        fetchSettings,
        updateSettings,
        updateSection,
        uploadLogo,
        uploadFavicon,
        uploadResume,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};
