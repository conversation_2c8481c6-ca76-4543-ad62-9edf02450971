# BSG Portfolio

A modern portfolio website built with React, Node.js, and MongoDB.

## Features

- Responsive design with dark mode support
- Blog system with admin panel
- Project showcase
- Contact form
- Real-time chat
- Services section
- Testimonials

## Deployment Instructions

### Backend Deployment (Render.com)

1. Create a new Web Service on Render.com
2. Connect your GitHub repository
3. Configure the following:
   - Build Command: `npm install`
   - Start Command: `npm start`
   - Environment Variables:
     ```
     MONGODB_URI=your_mongodb_uri
     JWT_SECRET=your_jwt_secret
     CLIENT_URL=https://your-frontend-url.netlify.app
     PORT=10000
     ```

### Frontend Deployment (Netlify)

1. Log in to Netlify
2. Click "New site from Git"
3. Connect your GitHub repository
4. Configure build settings:
   - Build Command: `npm run build`
   - Publish Directory: `dist`
5. Add environment variables:
   - `VITE_API_URL=https://your-backend-url.onrender.com/api`
   - `VITE_NODE_ENV=production`

### Post-Deployment Steps

1. Update `.env.production` with your backend URL:
   ```
   VITE_API_URL=https://your-backend-url.onrender.com/api
   VITE_NODE_ENV=production
   ```

2. Create an admin user:
   ```bash
   # On your local machine
   cd server
   npm run create-admin
   ```

3. Test the deployed application:
   - Visit your Netlify URL
   - Log in to the admin panel at `/admin`
   - Create a test blog post
   - Verify all features are working

## Development Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   # Frontend
   npm install

   # Backend
   cd server
   npm install
   ```

3. Create `.env` files:
   ```bash
   # Frontend .env
   VITE_API_URL=http://localhost:5001/api
   VITE_NODE_ENV=development

   # Backend .env
   MONGODB_URI=your_mongodb_uri
   JWT_SECRET=your_jwt_secret
   CLIENT_URL=http://localhost:5173
   PORT=5001
   ```

4. Start development servers:
   ```bash
   # Frontend
   npm run dev

   # Backend
   cd server
   npm run dev
   ```

## Technologies Used

- Frontend:
  - React
  - Vite
  - TailwindCSS
  - Framer Motion
  - Socket.io Client

- Backend:
  - Node.js
  - Express
  - MongoDB
  - Socket.io
  - JWT Authentication

## License

MIT License
