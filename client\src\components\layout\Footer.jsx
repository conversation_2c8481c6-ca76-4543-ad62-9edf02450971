import { motion } from 'framer-motion';
import { FaGithub, FaLinkedin, FaTwitter } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { useAccessibility } from '../../contexts/AccessibilityContext';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const { reducedMotion } = useAccessibility();

  const socialLinks = [
    {
      name: 'GitHub',
      url: 'https://github.com/bosona-gosaye', // Update with your actual GitHub username
      icon: FaGithub,
    },
    {
      name: 'LinkedIn',
      url: 'https://linkedin.com/in/bosona-gosaye', // Update with your actual LinkedIn profile
      icon: FaLinkedin,
    },
    {
      name: 'Twitter',
      url: 'https://twitter.com/bosona_gosaye', // Update with your actual Twitter handle
      icon: FaTwitter,
    },
  ];

  return (
    <footer className="bg-white dark:bg-gray-900 mt-auto border-t border-gray-200 dark:border-gray-800">
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-between space-y-6 md:flex-row md:space-y-0">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: reducedMotion ? 0.1 : 0.5 }}
            className="text-center md:text-left"
          >
            <p className="text-sm text-gray-500 dark:text-gray-400">
              © {currentYear} Bosona Gosaye. All rights reserved.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: reducedMotion ? 0.1 : 0.5, delay: reducedMotion ? 0 : 0.2 }}
            className="flex space-x-6"
          >
            {socialLinks.map((link) => (
              <motion.a
                key={link.name}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="sr-only">{link.name}</span>
                <link.icon className="h-6 w-6" />
              </motion.a>
            ))}
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: reducedMotion ? 0.1 : 0.5, delay: reducedMotion ? 0 : 0.4 }}
            className="text-center md:text-right"
          >
            <Link
              to="/contact"
              className="text-sm text-gray-500 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400 transition-colors duration-200"
            >
              Contact Me
            </Link>
          </motion.div>
        </div>
      </div>
    </footer>
  );
}
