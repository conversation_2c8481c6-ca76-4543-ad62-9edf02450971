import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash, FaStar, FaEye } from 'react-icons/fa';
import blogsApi from '../../services/blogsApi';
import Modal from './Modal';
import BlogForm from './BlogForm';
import DeleteConfirmation from './DeleteConfirmation';
import SEO from '../SEO';
import { toast } from 'react-toastify';
import Pagination from '../Pagination';

export default function BlogsManager() {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [isDeleting, setIsDeleting] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchBlogs();
  }, [pagination.page]);

  const fetchBlogs = async () => {
    try {
      const data = await blogsApi.getAllBlogs(pagination.page, pagination.limit);
      setBlogs(data.blogs);
      setPagination(prev => ({
        ...prev,
        total: data.pagination.total,
        totalPages: data.pagination.totalPages
      }));
    } catch (error) {
      console.error('Error fetching blogs:', error);
      toast.error('Failed to load blogs');
    } finally {
      setLoading(false);
    }
  };

  const handleAddClick = () => {
    setSelectedBlog(null);
    setModalMode('add');
    setIsModalOpen(true);
  };

  const handleEditClick = (blog) => {
    setSelectedBlog(blog);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDeleteClick = (blog) => {
    setSelectedBlog(blog);
    setIsDeleteModalOpen(true);
  };

  const handleSubmit = async (formData) => {
    try {
      if (modalMode === 'add') {
        await blogsApi.createBlog(formData);
        toast.success('Blog created successfully');
      } else {
        await blogsApi.updateBlog(selectedBlog._id, formData);
        toast.success('Blog updated successfully');
      }
      await fetchBlogs();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving blog:', error);
      toast.error('Failed to save blog');
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await blogsApi.deleteBlog(selectedBlog._id);
      toast.success('Blog deleted successfully');
      await fetchBlogs();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting blog:', error);
      toast.error('Failed to delete blog');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleFeatured = async (blog) => {
    try {
      await blogsApi.updateBlog(blog._id, {
        ...blog,
        isFeatured: !blog.isFeatured
      });
      toast.success(`Blog ${blog.isFeatured ? 'removed from' : 'added to'} featured`);
      await fetchBlogs();
    } catch (error) {
      console.error('Error updating blog:', error);
      toast.error('Failed to update blog');
    }
  };

  const handleTogglePublished = async (blog) => {
    try {
      await blogsApi.updateBlog(blog._id, {
        ...blog,
        isPublished: !blog.isPublished
      });
      toast.success(`Blog ${blog.isPublished ? 'unpublished' : 'published'}`);
      await fetchBlogs();
    } catch (error) {
      console.error('Error updating blog:', error);
      toast.error('Failed to update blog');
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <SEO title="Manage Blogs" />

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Manage Blogs
          </h2>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAddClick}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
          >
            <FaPlus />
            <span>Add Blog</span>
          </motion.button>
        </div>

        <div className="grid gap-4">
          {blogs.map((blog) => (
            <motion.div
              key={blog._id}
              layout
              className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow flex justify-between items-center"
            >
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {blog.title}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {blog.excerpt}
                </p>
                <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                  <span>{blog.category}</span>
                  <span>•</span>
                  <span>{blog.readTime} min read</span>
                  <span>•</span>
                  <span>{blog.views} views</span>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => handleTogglePublished(blog)}
                  className={`p-2 rounded-full ${
                    blog.isPublished
                      ? 'text-green-500 hover:text-green-600'
                      : 'text-gray-400 hover:text-gray-500'
                  }`}
                >
                  <FaEye />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => handleToggleFeatured(blog)}
                  className={`p-2 rounded-full ${
                    blog.isFeatured
                      ? 'text-yellow-500 hover:text-yellow-600'
                      : 'text-gray-400 hover:text-gray-500'
                  }`}
                >
                  <FaStar />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => handleEditClick(blog)}
                  className="p-2 text-blue-500 hover:text-blue-600 rounded-full"
                >
                  <FaEdit />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => handleDeleteClick(blog)}
                  className="p-2 text-red-500 hover:text-red-600 rounded-full"
                >
                  <FaTrash />
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        <Pagination
          currentPage={pagination.page}
          totalPages={pagination.totalPages}
          onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
        />
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={`${modalMode === 'add' ? 'Add' : 'Edit'} Blog`}
      >
        <BlogForm
          initialData={selectedBlog}
          onSubmit={handleSubmit}
          onCancel={() => setIsModalOpen(false)}
        />
      </Modal>

      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Blog"
      >
        <DeleteConfirmation
          itemType="blog"
          onConfirm={handleDelete}
          onCancel={() => setIsDeleteModalOpen(false)}
          isDeleting={isDeleting}
        />
      </Modal>
    </>
  );
}
