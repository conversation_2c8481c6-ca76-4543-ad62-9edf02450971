import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaSearch, FaTimes, FaFolder, FaFileAlt, FaUser } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { useAnalytics } from './Analytics';

// Search functionality
class SearchEngine {
  constructor() {
    this.index = [];
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Build search index from various sources
      this.index = [
        // Pages
        { type: 'page', title: 'Home', path: '/', content: 'portfolio developer designer full-stack' },
        { type: 'page', title: 'About', path: '/about', content: 'about me experience skills education timeline' },
        { type: 'page', title: 'Projects', path: '/projects', content: 'projects portfolio work showcase development' },
        { type: 'page', title: 'Services', path: '/services', content: 'services web development design consulting' },
        { type: 'page', title: 'Blog', path: '/blog', content: 'blog articles posts writing thoughts' },
        { type: 'page', title: 'Contact', path: '/contact', content: 'contact get in touch email message' },
        
        // Skills
        { type: 'skill', title: 'React', content: 'react javascript frontend library components' },
        { type: 'skill', title: 'Node.js', content: 'nodejs backend server javascript runtime' },
        { type: 'skill', title: 'TypeScript', content: 'typescript javascript types static typing' },
        { type: 'skill', title: 'MongoDB', content: 'mongodb database nosql document storage' },
        { type: 'skill', title: 'UI/UX Design', content: 'design user interface experience figma adobe' },
        { type: 'skill', title: 'Mobile Development', content: 'mobile react native flutter ios android' },
        
        // Technologies
        { type: 'tech', title: 'MERN Stack', content: 'mongodb express react nodejs full-stack' },
        { type: 'tech', title: 'Tailwind CSS', content: 'tailwind css styling responsive design' },
        { type: 'tech', title: 'Framer Motion', content: 'framer motion animation react library' },
        { type: 'tech', title: 'Socket.io', content: 'socketio realtime websockets communication' },
      ];

      // Add projects if available
      try {
        const projectsResponse = await fetch('/api/projects');
        if (projectsResponse.ok) {
          const projects = await projectsResponse.json();
          projects.forEach(project => {
            this.index.push({
              type: 'project',
              title: project.title,
              path: `/projects/${project._id}`,
              content: `${project.description} ${project.technologies?.join(' ') || ''}`
            });
          });
        }
      } catch (error) {
        console.warn('Could not load projects for search index');
      }

      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize search engine:', error);
    }
  }

  search(query) {
    if (!query.trim()) return [];

    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 1);
    const results = [];

    this.index.forEach(item => {
      let score = 0;
      const titleLower = item.title.toLowerCase();
      const contentLower = item.content.toLowerCase();

      searchTerms.forEach(term => {
        // Title matches get higher score
        if (titleLower.includes(term)) {
          score += titleLower === term ? 10 : 5;
        }
        
        // Content matches
        if (contentLower.includes(term)) {
          score += 1;
        }
      });

      if (score > 0) {
        results.push({ ...item, score });
      }
    });

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, 8); // Limit to top 8 results
  }
}

const searchEngine = new SearchEngine();

export const SearchModal = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef();
  const { searchPerformed } = useAnalytics();

  useEffect(() => {
    if (isOpen) {
      searchEngine.initialize();
      inputRef.current?.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    const timeoutId = setTimeout(() => {
      const searchResults = searchEngine.search(query);
      setResults(searchResults);
      setSelectedIndex(-1);
      setIsLoading(false);
      
      // Track search
      searchPerformed(query, searchResults.length);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, searchPerformed]);

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, -1));
    } else if (e.key === 'Enter' && selectedIndex >= 0) {
      e.preventDefault();
      const selectedResult = results[selectedIndex];
      if (selectedResult) {
        window.location.href = selectedResult.path;
        onClose();
      }
    }
  };

  const getIcon = (type) => {
    switch (type) {
      case 'project': return FaFolder;
      case 'page': return FaFileAlt;
      case 'skill':
      case 'tech': return FaUser;
      default: return FaFileAlt;
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'project': return 'Project';
      case 'page': return 'Page';
      case 'skill': return 'Skill';
      case 'tech': return 'Technology';
      default: return 'Result';
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-2xl mx-4"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Search Input */}
          <div className="flex items-center p-4 border-b border-gray-200 dark:border-gray-700">
            <FaSearch className="w-5 h-5 text-gray-400 mr-3" />
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Search projects, skills, pages..."
              className="flex-1 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none text-lg"
            />
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <FaTimes className="w-4 h-4" />
            </button>
          </div>

          {/* Search Results */}
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin w-6 h-6 border-2 border-indigo-600 border-t-transparent rounded-full mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">Searching...</p>
              </div>
            ) : results.length > 0 ? (
              <div className="py-2">
                {results.map((result, index) => {
                  const Icon = getIcon(result.type);
                  const isSelected = index === selectedIndex;
                  
                  return (
                    <Link
                      key={`${result.type}-${result.title}`}
                      to={result.path}
                      onClick={onClose}
                      className={`flex items-center px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                        isSelected ? 'bg-gray-50 dark:bg-gray-700' : ''
                      }`}
                    >
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mr-3">
                        <Icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-gray-900 dark:text-white font-medium">
                          {result.title}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {getTypeLabel(result.type)}
                        </p>
                      </div>
                    </Link>
                  );
                })}
              </div>
            ) : query.trim() ? (
              <div className="p-8 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  No results found for "{query}"
                </p>
              </div>
            ) : (
              <div className="p-8 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  Start typing to search...
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default SearchModal;
