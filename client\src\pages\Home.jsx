import { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useSpring, useInView } from 'framer-motion';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { FaGith<PERSON>, Fa<PERSON><PERSON>ed<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaArrowDown } from 'react-icons/fa';
import { HiCode, HiOutlineHome, HiDeviceMobile, HiColorSwatch } from 'react-icons/hi';
import { Link } from 'react-router-dom';
// import { useTheme } from '../contexts/ThemeContext';
import projectsApi from '../services/projectsApi';
import LoadingSpinner from '../components/LoadingSpinner';
import SEO from '../components/SEO';

gsap.registerPlugin(ScrollTrigger);

const skills = [
  { name: 'Full-Stack Development', icon: HiCode, description: 'Building scalable web applications with MERN stack, TypeScript, and modern frameworks' },
  { name: 'Frontend Excellence', icon: HiOutlineHome, description: 'Creating responsive, accessible UIs with React, Vue.js, and advanced CSS frameworks' },
  { name: 'Mobile Development', icon: HiDeviceMobile, description: 'Developing cross-platform mobile apps with React Native and Flutter' },
  { name: 'UI/UX Design', icon: HiColorSwatch, description: 'Designing user-centered interfaces with Figma, prototyping, and user research' },
];

const Section = ({ children, className = '' }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-20% 0px" });
  
  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default function Home() {
  // const { theme } = useTheme();
  const [isHovered, setIsHovered] = useState(null);
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, { stiffness: 100, damping: 30 });

  // State for projects - moved inside the component
  const [projects, setProjects] = useState([]);
  const [projectsLoading, setProjectsLoading] = useState(true);
  const [projectsError, setProjectsError] = useState(null);

  const heroRef = useRef(null);
  // const skillsRef = useRef(null);
  // const projectsRef = useRef(null);
  
  // Helper function to get image URL
  const getImageUrl = (image) => {
    if (!image) return '/placeholder-project.svg';
    // If URL starts with http, it's already an absolute URL
    if (image.url.startsWith('http')) return image.url;
    
    // For relative URLs, we need to construct the full URL
    // The API URL from env is 'http://localhost:5001/api', but uploads are served from '/uploads'
    // So we need to remove '/api' and add '/uploads' to get the correct path
    const baseUrl = import.meta.env.VITE_API_URL || 'https://portfolio-s11i.onrender.com/api';
    const serverUrl = baseUrl.endsWith('/api') ? baseUrl.slice(0, -4) : baseUrl;
    
    // If the image URL already starts with '/uploads', use it as is
    // Otherwise, prepend '/uploads' to the URL
    const imagePath = image.url.startsWith('/uploads') ? image.url : `/uploads${image.url}`;
    
    return `${serverUrl}${imagePath}`;
  };

  // Fetch projects on component mount
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setProjectsLoading(true);
        const data = await projectsApi.getPublicProjects();
        // Only take the first 3 projects
        setProjects(data.slice(0, 3));
        setProjectsError(null);
      } catch (error) {
        console.error('Error fetching projects:', error);
        setProjectsError('Failed to load projects');
      } finally {
        setProjectsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  useEffect(() => {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: heroRef.current,
        start: "top center",
        end: "bottom center",
        toggleActions: "play none none reverse"
      }
    });

    tl.from(heroRef.current.querySelectorAll('.animate-hero'), {
      y: 100,
      opacity: 0,
      duration: 0.8,
      stagger: 0.2,
      ease: "power3.out"
    });
  }, []);

  return (
    <>
      <SEO
        title="Home"
        description="Welcome to Bosona Gosaye's portfolio - Full-Stack Developer, UX/UI Designer, and Mobile App Developer specializing in modern web technologies and user-centered design."
        keywords={['portfolio', 'home', 'developer', 'designer']}
      />

      {/* Progress Bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-indigo-600 origin-left z-50"
        style={{ scaleX }}
      />

      <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        {/* Hero Section */}
        <div ref={heroRef} className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 sm:pt-24 lg:pt-32 pb-16 sm:pb-20">
          <div className="flex flex-col-reverse lg:flex-row items-center justify-between gap-8 sm:gap-12 lg:gap-16">
            <div className="flex-1 text-center lg:text-left space-y-6 sm:space-y-8">
              <motion.h1
                className="animate-hero text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                Hi, I'm{" "}
                <span className="relative">
                  <span className="relative z-10 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600">
                    Bosona
                  </span>
                  <motion.span
                    className="absolute -inset-1 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg z-0"
                    layoutId="highlight"
                  />
                </span>{" "}
                <span className="relative">
                  <span className="relative z-10 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600">
                    Gosaye
                  </span>
                </span>
              </motion.h1>

              <motion.p
                className="animate-hero text-lg sm:text-xl md:text-2xl lg:text-2xl text-gray-600 dark:text-gray-300 max-w-2xl lg:max-w-none"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Full-Stack Developer & UX/UI Designer crafting exceptional digital experiences with modern technologies
              </motion.p>

              <motion.div
                className="animate-hero flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <Link
                  to="/projects"
                  className="group relative px-6 sm:px-8 py-3 sm:py-4 bg-indigo-600 text-white rounded-lg overflow-hidden text-center sm:text-left font-semibold transition-all duration-300 hover:shadow-xl hover:scale-105"
                >
                  <span className="relative z-10">View Projects</span>
                  <motion.div
                    className="absolute inset-0 bg-indigo-700"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: 0 }}
                    transition={{ duration: 0.3 }}
                  />
                </Link>
                <Link
                  to="/contact"
                  className="group relative px-6 sm:px-8 py-3 sm:py-4 border-2 border-indigo-600 text-indigo-600 dark:text-indigo-400 rounded-lg overflow-hidden text-center sm:text-left font-semibold transition-all duration-300 hover:shadow-xl hover:scale-105"
                >
                  <span className="relative z-10">Contact Me</span>
                  <motion.div
                    className="absolute inset-0 bg-indigo-50 dark:bg-indigo-900/30"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: 0 }}
                    transition={{ duration: 0.3 }}
                  />
                </Link>
              </motion.div>

              <motion.div
                className="animate-hero flex gap-6 justify-center lg:justify-start text-gray-600 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                {[
                  { Icon: FaGithub, url: 'https://github.com/bosona-gosaye', label: 'GitHub' },
                  { Icon: FaLinkedin, url: 'https://linkedin.com/in/bosona-gosaye', label: 'LinkedIn' },
                  { Icon: FaTwitter, url: 'https://twitter.com/bosona_gosaye', label: 'Twitter' }
                ].map(({ Icon, url, label }, index) => (
                  <motion.a
                    key={index}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={label}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    className="text-2xl hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    <Icon />
                  </motion.a>
                ))}
              </motion.div>
            </div>
            
            <motion.div
              className="flex-1 flex justify-center lg:justify-end"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="relative">
                <motion.div
                  className="w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 lg:w-80 lg:h-80 xl:w-96 xl:h-96 rounded-full overflow-hidden shadow-2xl"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src="/profile/bosona.jpg"
                    alt="Bosona Gosaye"
                    className="w-full h-full object-cover"
                  />
                </motion.div>
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 360],
                    borderRadius: ["50%", "30%", "50%"]
                  }}
                  transition={{
                    duration: 8,
                    ease: "easeInOut",
                    repeat: Infinity,
                  }}
                  className="absolute -z-10 inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 opacity-20 blur-2xl"
                />
                {/* Floating elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -top-4 -right-4 w-8 h-8 bg-indigo-500 rounded-full opacity-60"
                />
                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -bottom-6 -left-6 w-6 h-6 bg-purple-500 rounded-full opacity-60"
                />
              </div>
            </motion.div>
          </div>

          <motion.div
            className="absolute bottom-8 left-1/2 -translate-x-1/2 text-gray-400 dark:text-gray-500 animate-bounce"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
          >
            <FaArrowDown className="w-6 h-6" />
          </motion.div>
        </div>

        {/* Skills Section */}
        <Section className="py-16 sm:py-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 text-gray-900 dark:text-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            My <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Expertise</span>
          </motion.h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            {skills.map((skill, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -10, scale: 1.02 }}
                viewport={{ once: true }}
                className="group p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
              >
                <motion.div
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  transition={{ duration: 0.5 }}
                  className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 rounded-xl flex items-center justify-center mb-4 sm:mb-6 group-hover:shadow-lg"
                >
                  <skill.icon className="w-7 h-7 sm:w-8 sm:h-8 text-indigo-600 dark:text-indigo-400" />
                </motion.div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                  {skill.name}
                </h3>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 leading-relaxed">{skill.description}</p>
              </motion.div>
            ))}
          </div>
        </Section>

        {/* Featured Projects Section */}
        <Section className="py-16 sm:py-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 text-gray-900 dark:text-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Featured <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Projects</span>
          </motion.h2>
          
          {projectsLoading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner />
            </div>
          ) : projectsError ? (
            <div className="text-center text-red-500 py-8">{projectsError}</div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
              {projects.length > 0 ? projects.map((project, index) => (
                <Link
                  key={project._id}
                  to={`/projects/${project._id}`}
                  onMouseEnter={() => setIsHovered(index)}
                  onMouseLeave={() => setIsHovered(null)}
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ y: -10, scale: 1.02 }}
                    viewport={{ once: true }}
                    className="group bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
                  >
                    <div className="relative h-48 sm:h-56 lg:h-48 xl:h-56 overflow-hidden">
                      <motion.img
                        src={project.images && project.images[0] ? getImageUrl(project.images[0]) : '/placeholder-project.svg'}
                        alt={project.images && project.images[0] ? project.images[0].alt : project.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      {isHovered === index && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="absolute inset-0 bg-black/50 flex items-center justify-center"
                        >
                          <motion.span
                            className="text-white font-semibold px-4 py-2 bg-indigo-600 rounded-lg"
                            whileHover={{ scale: 1.05 }}
                          >
                            View Project
                          </motion.span>
                        </motion.div>
                      )}
                    </div>
                    <div className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">{project.title}</h3>
                      <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-4 line-clamp-2 leading-relaxed">{project.description}</p>
                      <div className="flex flex-wrap gap-2">
                        {project.technologies && project.technologies.slice(0, 3).map((tech, idx) => (
                          <motion.span
                            key={idx}
                            className="px-3 py-1 text-xs font-medium bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 rounded-full border border-indigo-200 dark:border-indigo-700"
                            whileHover={{ scale: 1.05 }}
                          >
                            {tech}
                          </motion.span>
                        ))}
                        {project.technologies && project.technologies.length > 3 && (
                          <span className="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">
                            +{project.technologies.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  </motion.div>
                </Link>
              )) : (
                <div className="col-span-3 text-center py-12 text-gray-500 dark:text-gray-400">
                  No projects to display
                </div>
              )}
            </div>
          )}
          
          <div className="mt-12 text-center">
            <Link 
              to="/projects"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
            >
              View All Projects
            </Link>
          </div>
        </Section>

        {/* Testimonials Section */}
        <Section className="py-16 sm:py-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-center mb-8 sm:mb-12 text-gray-900 dark:text-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            What <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Clients Say</span>
          </motion.h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {[
              {
                name: "Sarah Johnson",
                position: "Product Manager",
                company: "TechCorp",
                content: "Bosona delivered an exceptional web application that exceeded our expectations. His attention to detail and technical expertise are outstanding.",
                rating: 5
              },
              {
                name: "Michael Chen",
                position: "Startup Founder",
                company: "InnovateLab",
                content: "Working with Bosona was a game-changer for our startup. He transformed our ideas into a beautiful, functional platform.",
                rating: 5
              },
              {
                name: "Emily Rodriguez",
                position: "Design Director",
                company: "CreativeStudio",
                content: "Bosona's combination of technical skills and design sensibility is rare. He truly understands both user experience and development.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                viewport={{ once: true }}
                className="group bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
              >
                <div className="flex items-center mb-4 sm:mb-6">
                  {[...Array(5)].map((_, i) => (
                    <motion.svg
                      key={i}
                      className={`w-5 h-5 sm:w-6 sm:h-6 ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      transition={{ duration: 0.3 }}
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </motion.svg>
                  ))}
                </div>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 italic leading-relaxed">
                  "{testimonial.content}"
                </p>
                <div>
                  <h4 className="font-bold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                    {testimonial.name}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {testimonial.position} at {testimonial.company}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
          <div className="mt-12 text-center">
            <Link
              to="/testimonials"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
            >
              View All Testimonials
            </Link>
          </div>
        </Section>
      </div>
    </>
  );
}
