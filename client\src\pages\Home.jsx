import { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useSpring, useInView } from 'framer-motion';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { FaGith<PERSON>, Fa<PERSON><PERSON>ed<PERSON>, <PERSON>a<PERSON><PERSON>tter, FaArrowDown } from 'react-icons/fa';
import { HiCode, HiOutlineHome, HiDeviceMobile, HiColorSwatch } from 'react-icons/hi';
import { Link } from 'react-router-dom';
// import { useTheme } from '../contexts/ThemeContext';
import projectsApi from '../services/projectsApi';
import LoadingSpinner from '../components/LoadingSpinner';

gsap.registerPlugin(ScrollTrigger);

const skills = [
  { name: 'Web Development', icon: HiCode, description: 'Building responsive web applications with modern technologies' },
  { name: 'Frontend Development', icon: HiOutlineHome, description: 'Creating robust client-side solutions with scalable UI/UX' },
  { name: 'Mobile Development', icon: HiDeviceMobile, description: 'Developing cross-platform mobile apps for iOS and Android' },
  { name: 'UI/UX Design', icon: HiColorSwatch, description: 'Designing intuitive and engaging user interfaces' },
];

const Section = ({ children, className = '' }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-20% 0px" });
  
  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default function Home() {
  // const { theme } = useTheme();
  const [isHovered, setIsHovered] = useState(null);
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, { stiffness: 100, damping: 30 });

  // State for projects - moved inside the component
  const [projects, setProjects] = useState([]);
  const [projectsLoading, setProjectsLoading] = useState(true);
  const [projectsError, setProjectsError] = useState(null);

  const heroRef = useRef(null);
  // const skillsRef = useRef(null);
  // const projectsRef = useRef(null);
  
  // Helper function to get image URL
  const getImageUrl = (image) => {
    if (!image) return '/placeholder-project.svg';
    // If URL starts with http, it's already an absolute URL
    if (image.url.startsWith('http')) return image.url;
    
    // For relative URLs, we need to construct the full URL
    // The API URL from env is 'http://localhost:5001/api', but uploads are served from '/uploads'
    // So we need to remove '/api' and add '/uploads' to get the correct path
    const baseUrl = import.meta.env.VITE_API_URL || 'https://portfolio-s11i.onrender.com/api';
    const serverUrl = baseUrl.endsWith('/api') ? baseUrl.slice(0, -4) : baseUrl;
    
    // If the image URL already starts with '/uploads', use it as is
    // Otherwise, prepend '/uploads' to the URL
    const imagePath = image.url.startsWith('/uploads') ? image.url : `/uploads${image.url}`;
    
    return `${serverUrl}${imagePath}`;
  };

  // Fetch projects on component mount
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setProjectsLoading(true);
        const data = await projectsApi.getPublicProjects();
        // Only take the first 3 projects
        setProjects(data.slice(0, 3));
        setProjectsError(null);
      } catch (error) {
        console.error('Error fetching projects:', error);
        setProjectsError('Failed to load projects');
      } finally {
        setProjectsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  useEffect(() => {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: heroRef.current,
        start: "top center",
        end: "bottom center",
        toggleActions: "play none none reverse"
      }
    });

    tl.from(heroRef.current.querySelectorAll('.animate-hero'), {
      y: 100,
      opacity: 0,
      duration: 0.8,
      stagger: 0.2,
      ease: "power3.out"
    });
  }, []);

  return (
    <>
      {/* Progress Bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-indigo-600 origin-left z-50"
        style={{ scaleX }}
      />

      <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        {/* Hero Section */}
        <div ref={heroRef} className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
          <div className="flex flex-col-reverse lg:flex-row items-center justify-between gap-12">
            <div className="flex-1 text-center lg:text-left space-y-8">
              <motion.h1
                className="animate-hero text-5xl md:text-7xl font-bold leading-tight"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                Hi, I'm{" "}
                <span className="relative">
                  <span className="relative z-10 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600">
                    Bosona
                  </span>
                  <motion.span
                    className="absolute -inset-1 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg z-0"
                    layoutId="highlight"
                  />
                </span>{" "}
                <span className="relative">
                  <span className="relative z-10 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600">
                    Gosaye
                  </span>
                </span>
              </motion.h1>

              <motion.p
                className="animate-hero text-xl md:text-2xl text-gray-600 dark:text-gray-300"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                A Full Stack Developer passionate about creating impactful web solutions
              </motion.p>

              <motion.div
                className="animate-hero flex gap-4 justify-center lg:justify-start"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <Link
                  to="/projects"
                  className="group relative px-6 py-3 bg-indigo-600 text-white rounded-lg overflow-hidden"
                >
                  <span className="relative z-10">View Projects</span>
                  <motion.div
                    className="absolute inset-0 bg-indigo-700"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: 0 }}
                    transition={{ duration: 0.3 }}
                  />
                </Link>
                <Link
                  to="/contact"
                  className="group relative px-6 py-3 border-2 border-indigo-600 text-indigo-600 dark:text-indigo-400 rounded-lg overflow-hidden"
                >
                  <span className="relative z-10">Contact Me</span>
                  <motion.div
                    className="absolute inset-0 bg-indigo-50 dark:bg-indigo-900/30"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: 0 }}
                    transition={{ duration: 0.3 }}
                  />
                </Link>
              </motion.div>

              <motion.div
                className="animate-hero flex gap-6 justify-center lg:justify-start text-gray-600 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                {[FaGithub, FaLinkedin, FaTwitter].map((Icon, index) => (
                  <motion.a
                    key={index}
                    href="#"
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    className="text-2xl hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                  >
                    <Icon />
                  </motion.a>
                ))}
              </motion.div>
            </div>
            
            <motion.div
              className="flex-1 flex justify-center lg:justify-end"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="relative">
                <div className="w-80 h-80 md:w-96 md:h-96 rounded-full overflow-hidden">
                  <img
                    src="/profile/bosona.jpg"
                    alt="Bosona Gosaye"
                    className="w-full h-full object-cover"
                  />
                </div>
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 360],
                    borderRadius: ["50%", "30%", "50%"]
                  }}
                  transition={{
                    duration: 8,
                    ease: "easeInOut",
                    repeat: Infinity,
                  }}
                  className="absolute -z-10 inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 opacity-20 blur-2xl"
                />
              </div>
            </motion.div>
          </div>

          <motion.div
            className="absolute bottom-8 left-1/2 -translate-x-1/2 text-gray-400 dark:text-gray-500 animate-bounce"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
          >
            <FaArrowDown className="w-6 h-6" />
          </motion.div>
        </div>

        {/* Skills Section */}
        <Section className="py-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">
            My Expertise
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {skills.map((skill, index) => (
              <motion.div
                key={index}
                whileHover={{ y: -10 }}
                className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                  className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mb-4"
                >
                  <skill.icon className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                </motion.div>
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                  {skill.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">{skill.description}</p>
              </motion.div>
            ))}
          </div>
        </Section>

        {/* Featured Projects Section */}
        <Section className="py-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">
            My Projects
          </h2>
          
          {projectsLoading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner />
            </div>
          ) : projectsError ? (
            <div className="text-center text-red-500 py-8">{projectsError}</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {projects.length > 0 ? projects.map((project, index) => (
                <Link
                  key={project._id}
                  to={`/projects/${project._id}`}
                  onMouseEnter={() => setIsHovered(index)}
                  onMouseLeave={() => setIsHovered(null)}
                >
                  <motion.div
                    whileHover={{ y: -10 }}
                    className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <div className="relative h-48 overflow-hidden">
                      <motion.img
                        src={project.images && project.images[0] ? getImageUrl(project.images[0]) : '/placeholder-project.svg'}
                        alt={project.images && project.images[0] ? project.images[0].alt : project.title}
                        className="w-full h-full object-cover"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      />
                      {isHovered === index && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="absolute inset-0 bg-black/50 flex items-center justify-center"
                        >
                          <span className="text-white">View Project</span>
                        </motion.div>
                      )}
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{project.title}</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">{project.description}</p>
                      <div className="flex flex-wrap gap-2">
                        {project.technologies && project.technologies.slice(0, 3).map((tech, idx) => (
                          <span 
                            key={idx} 
                            className="px-2 py-1 text-xs bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300 rounded-full"
                          >
                            {tech}
                          </span>
                        ))}
                        {project.technologies && project.technologies.length > 3 && (
                          <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 rounded-full">
                            +{project.technologies.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  </motion.div>
                </Link>
              )) : (
                <div className="col-span-3 text-center py-12 text-gray-500 dark:text-gray-400">
                  No projects to display
                </div>
              )}
            </div>
          )}
          
          <div className="mt-12 text-center">
            <Link 
              to="/projects"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
            >
              View All Projects
            </Link>
          </div>
        </Section>
      </div>
    </>
  );
}
