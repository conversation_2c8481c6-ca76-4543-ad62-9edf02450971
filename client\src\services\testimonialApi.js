import api from './api';

const testimonialApi = {
  getAllTestimonials: async () => {
    const response = await api.get('/testimonials/admin');
    return response.data;
  },

  getPublicTestimonials: async () => {
    const response = await api.get('/testimonials');
    return response.data;
  },

  getTestimonial: async (id) => {
    const response = await api.get(`/testimonials/${id}`);
    return response.data;
  },

  createTestimonial: async (testimonialData) => {
    const formData = new FormData();
    
    // Handle avatar upload
    if (testimonialData.avatar) {
      formData.append('avatar', testimonialData.avatar);
      delete testimonialData.avatar;
    }
    
    // Append other data
    Object.keys(testimonialData).forEach(key => {
      formData.append(key, testimonialData[key]);
    });

    const response = await api.post('/testimonials', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  updateTestimonial: async (id, testimonialData) => {
    const formData = new FormData();
    
    // Handle avatar upload
    if (testimonialData.avatar) {
      formData.append('avatar', testimonialData.avatar);
      delete testimonialData.avatar;
    }
    
    // Append other data
    Object.keys(testimonialData).forEach(key => {
      formData.append(key, testimonialData[key]);
    });

    const response = await api.put(`/testimonials/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  deleteTestimonial: async (id) => {
    const response = await api.delete(`/testimonials/${id}`);
    return response.data;
  },

  reorderTestimonials: async (testimonials) => {
    const response = await api.post('/testimonials/reorder', { testimonials });
    return response.data;
  }
};

export default testimonialApi;
