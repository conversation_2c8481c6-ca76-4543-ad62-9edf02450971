import { motion } from 'framer-motion';
import { <PERSON>a<PERSON>lock, FaT<PERSON><PERSON>, Fa<PERSON>ser, FaCalendar } from 'react-icons/fa';
import { Link } from 'react-router-dom';

export default function BlogPost({ post, isPreview = false }) {
  const {
    title,
    excerpt,
    slug,
    readTime,
    tags,
    author,
    createdAt,
    thumbnail,
  } = post;

  const formattedDate = new Date(createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
    >
      <Link to={`/blog/${slug}`}>
        <div className="relative h-48 md:h-64 overflow-hidden">
          <motion.img
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
            src={thumbnail}
            alt={title}
            className="w-full h-full object-cover"
          />
        </div>
      </Link>

      <div className="p-6">
        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4 space-x-4">
          <div className="flex items-center">
            <FaUser className="mr-2" />
            <span>{author.username}</span>
          </div>
          <div className="flex items-center">
            <FaCalendar className="mr-2" />
            <span>{formattedDate}</span>
          </div>
          <div className="flex items-center">
            <FaClock className="mr-2" />
            <span>{readTime} min read</span>
          </div>
        </div>

        <Link to={`/blog/${slug}`}>
          <h2 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-4 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
            {title}
          </h2>
        </Link>

        <p className="text-gray-600 dark:text-gray-300 mb-4">
          {excerpt}
        </p>

        <div className="flex flex-wrap gap-2 mb-4">
          {tags.map((tag) => (
            <Link
              key={tag}
              to={`/blog?tag=${tag}`}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-indigo-100 dark:hover:bg-indigo-900 transition-colors"
            >
              <FaTags className="mr-2" />
              {tag}
            </Link>
          ))}
        </div>

        {!isPreview && (
          <Link
            to={`/blog/${slug}`}
            className="inline-flex items-center text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 transition-colors"
          >
            Read More
            <svg
              className="ml-2 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        )}
      </div>
    </motion.article>
  );
}
