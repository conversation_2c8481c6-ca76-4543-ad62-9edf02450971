import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ServicesManager from '../ServicesManager';
import { serviceService } from '../../../services/serviceService';

jest.mock('../../../services/serviceService');

describe('ServicesManager', () => {
  const mockServices = [
    {
      id: 1,
      title: 'Web Development',
      description: 'Full stack web development',
      icon: 'code'
    }
  ];

  beforeEach(() => {
    serviceService.getAllServices.mockResolvedValue(mockServices);
  });

  test('renders services list', async () => {
    render(<ServicesManager />);
    
    await waitFor(() => {
      expect(screen.getByText('Web Development')).toBeInTheDocument();
      expect(screen.getByText('Full stack web development')).toBeInTheDocument();
    });
  });

  test('opens add service modal', () => {
    render(<ServicesManager />);
    
    fireEvent.click(screen.getByText('Add Service'));
    expect(screen.getByText('Add New Service')).toBeInTheDocument();
  });

  test('edits service', async () => {
    render(<ServicesManager />);
    
    await waitFor(() => {
      const editButton = screen.getByLabelText('Edit service');
      fireEvent.click(editButton);
      expect(screen.getByText('Edit Service')).toBeInTheDocument();
    });
  });

  test('deletes service', async () => {
    const deleteMock = jest.fn();
    serviceService.deleteService = deleteMock;

    render(<ServicesManager />);
    
    await waitFor(() => {
      const deleteButton = screen.getByLabelText('Delete service');
      fireEvent.click(deleteButton);
      const confirmButton = screen.getByText('Confirm Delete');
      fireEvent.click(confirmButton);
      expect(deleteMock).toHaveBeenCalledWith(1);
    });
  });
});
