import { useState, useEffect } from 'react';
import { io } from 'socket.io-client';
import axios from 'axios';
import ChatList from './ChatList';
import ChatWindow from './ChatWindow';
import { useAuth } from '../../contexts/AuthContext';

export default function ChatManager() {
  const [chats, setChats] = useState([]);
  const [selectedChat, setSelectedChat] = useState(null);
  const [socket, setSocket] = useState(null);
  const { token } = useAuth();

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io('https://portfolio-s11i.onrender.com', {
      auth: {
        token
      }
    });
    setSocket(newSocket);

    // Fetch initial chats
    fetchChats();

    return () => {
      newSocket.close();
    };
  }, [token]);

  useEffect(() => {
    if (!socket) return;

    // Listen for new messages
    socket.on('receive_message', ({ chatId, message }) => {
      setChats(prevChats => {
        return prevChats.map(chat => {
          if (chat._id === chatId) {
            return {
              ...chat,
              messages: [...chat.messages, message],
              lastUpdated: new Date()
            };
          }
          return chat;
        });
      });
    });

    return () => {
      socket.off('receive_message');
    };
  }, [socket]);

  const fetchChats = async () => {
    try {
      const response = await axios.get('http://localhost:5000/api/chat', {
        headers: { Authorization: `Bearer ${token}` }
      });
      setChats(response.data);
    } catch (error) {
      console.error('Error fetching chats:', error);
    }
  };

  const handleSelectChat = async (chat) => {
    setSelectedChat(chat);
    
    // Mark messages as read
    if (chat.messages.some(m => !m.read && m.sender === 'user')) {
      try {
        const response = await axios.patch(
          `http://localhost:5000/api/chat/${chat._id}/read`,
          {},
          { headers: { Authorization: `Bearer ${token}` } }
        );
        
        setChats(prevChats => 
          prevChats.map(c => 
            c._id === chat._id ? response.data : c
          )
        );
      } catch (error) {
        console.error('Error marking messages as read:', error);
      }
    }

    // Join chat room
    socket?.emit('join_chat', chat.userId);
  };

  const handleSendMessage = async (message) => {
    if (!selectedChat || !socket) return;

    try {
      // Emit message through socket
      socket.emit('send_message', {
        chatId: selectedChat._id,
        message
      });

      // Update local state
      setChats(prevChats => {
        return prevChats.map(chat => {
          if (chat._id === selectedChat._id) {
            return {
              ...chat,
              messages: [...chat.messages, message],
              lastUpdated: new Date()
            };
          }
          return chat;
        });
      });

      setSelectedChat(prev => ({
        ...prev,
        messages: [...prev.messages, message],
        lastUpdated: new Date()
      }));
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleUpdateStatus = async (status) => {
    if (!selectedChat) return;

    try {
      const response = await axios.patch(
        `http://localhost:5000/api/chat/${selectedChat._id}/status`,
        { status },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      setChats(prevChats =>
        prevChats.map(chat =>
          chat._id === selectedChat._id ? response.data : chat
        )
      );
      setSelectedChat(response.data);
    } catch (error) {
      console.error('Error updating chat status:', error);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <ChatList
            chats={chats}
            selectedChatId={selectedChat?._id}
            onSelectChat={handleSelectChat}
          />
        </div>
        <div className="lg:col-span-2 h-[700px]">
          <ChatWindow
            chat={selectedChat}
            socket={socket}
            onSendMessage={handleSendMessage}
            onUpdateStatus={handleUpdateStatus}
          />
        </div>
      </div>
    </div>
  );
}
