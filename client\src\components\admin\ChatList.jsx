import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { FaCircle } from 'react-icons/fa';

export default function ChatList({ chats, onSelectChat, selectedChatId }) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Messages</h2>
      </div>
      <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-[600px] overflow-y-auto">
        {chats.map((chat) => (
          <motion.div
            key={chat._id}
            whileHover={{ backgroundColor: 'rgba(0,0,0,0.05)' }}
            className={`p-4 cursor-pointer ${
              selectedChatId === chat._id ? 'bg-indigo-50 dark:bg-indigo-900/30' : ''
            }`}
            onClick={() => onSelectChat(chat)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                  {chat.userName}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {chat.userEmail}
                </p>
              </div>
              <div className="flex flex-col items-end">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {format(new Date(chat.lastUpdated), 'MMM d, h:mm a')}
                </span>
                {chat.messages.some(m => !m.read && m.sender === 'user') && (
                  <FaCircle className="text-indigo-600 dark:text-indigo-400 text-xs mt-1" />
                )}
              </div>
            </div>
            {chat.messages.length > 0 && (
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 truncate">
                {chat.messages[chat.messages.length - 1].content}
              </p>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );
}
