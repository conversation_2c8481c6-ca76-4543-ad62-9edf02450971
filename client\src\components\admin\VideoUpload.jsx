import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaUpload, Fa<PERSON>pinner, FaVideo, FaTrash } from 'react-icons/fa';
import axios from 'axios';

export default function VideoUpload({ label, currentVideo, onUpload, className = '' }) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState(currentVideo?.url || currentVideo);
  const [videoTitle, setVideoTitle] = useState(currentVideo?.title || '');
  const [videoDescription, setVideoDescription] = useState(currentVideo?.description || '');
  const fileInputRef = useRef();

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFile = async (file) => {
    if (!file.type.startsWith('video/')) {
      alert('Please upload a video file');
      return;
    }

    setIsUploading(true);
    try {
      // Create FormData
      const formData = new FormData();
      formData.append('video', file);

      // Upload to server - using the video-specific endpoint
      const response = await axios.post('https://portfolio-s11i.onrender.com/api/upload/video', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // Set preview URL
      const videoUrl = `https://portfolio-s11i.onrender.com${response.data.url}`;
      setPreview(videoUrl);

      // Call onUpload with the server response and metadata
      onUpload({
        url: videoUrl,
        title: videoTitle || file.name,
        description: videoDescription || ''
      });
    } catch (error) {
      console.error('Error uploading video:', error);
      alert('Failed to upload video');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemove = () => {
    setPreview(null);
    setVideoTitle('');
    setVideoDescription('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onUpload(null);
  };

  const handleTitleChange = (e) => {
    const newTitle = e.target.value;
    setVideoTitle(newTitle);
    if (preview) {
      onUpload({
        url: preview,
        title: newTitle,
        description: videoDescription
      });
    }
  };

  const handleDescriptionChange = (e) => {
    const newDescription = e.target.value;
    setVideoDescription(newDescription);
    if (preview) {
      onUpload({
        url: preview,
        title: videoTitle,
        description: newDescription
      });
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      
      <motion.div
        className={`relative border-2 border-dashed rounded-lg p-4 text-center ${
          isDragging
            ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20'
            : 'border-gray-300 dark:border-gray-600'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        {isUploading ? (
          <div className="flex flex-col items-center justify-center py-4">
            <FaSpinner className="w-8 h-8 animate-spin text-indigo-600" />
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Uploading...</p>
          </div>
        ) : preview ? (
          <div className="relative">
            <video
              src={typeof preview === 'string' ? preview : preview.url}
              controls
              className="mx-auto max-h-48 rounded-lg object-contain"
            />
            <button
              type="button"
              onClick={handleRemove}
              className="absolute top-0 right-0 -mt-2 -mr-2 rounded-full bg-red-500 p-1 text-white shadow-lg hover:bg-red-600 focus:outline-none"
            >
              <FaTrash className="w-4 h-4" />
            </button>
            
            <div className="mt-2">
              <input
                type="text"
                value={videoTitle}
                onChange={handleTitleChange}
                placeholder="Video title"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
              />
              <textarea
                value={videoDescription}
                onChange={handleDescriptionChange}
                placeholder="Video description"
                rows="2"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
              />
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-4">
            <FaVideo className="w-8 h-8 text-gray-400" />
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Drag and drop a video here, or click to select
            </p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Supported formats: MP4, WebM, OGG (max 50MB)
            </p>
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleFileSelect}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
          </div>
        )}
      </motion.div>
    </div>
  );
}