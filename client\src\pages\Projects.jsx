import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGithub, FaExternalLinkAlt } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import projectsApi from '../services/projectsApi';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

export default function Projects() {
  const [projects, setProjects] = useState([]);
  const [categories, setCategories] = useState(['All']);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [hoveredProject, setHoveredProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Helper function to get image URL
  const getImageUrl = (image) => {
    if (!image) return '/placeholder-project.svg';
    // If URL starts with http, it's already an absolute URL
    if (image.url.startsWith('http')) return image.url;
    
    // For relative URLs, we need to construct the full URL
    // The API URL from env is 'http://localhost:5001/api', but uploads are served from '/uploads'
    // So we need to remove '/api' and add '/uploads' to get the correct path
    const baseUrl = import.meta.env.VITE_API_URL || 'https://portfolio-s11i.onrender.com/api';
    const serverUrl = baseUrl.endsWith('/api') ? baseUrl.slice(0, -4) : baseUrl;
    
    // If the image URL already starts with '/uploads', use it as is
    // Otherwise, prepend '/uploads' to the URL
    const imagePath = image.url.startsWith('/uploads') ? image.url : `/uploads${image.url}`;
    
    return `${serverUrl}${imagePath}`;
  };

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const data = await projectsApi.getPublicProjects();
      setProjects(data);
      
      // Extract unique categories
      const uniqueCategories = ['All', ...new Set(data.map(project => project.category))];
      setCategories(uniqueCategories);
    } catch (err) {
      console.error('Error loading projects:', err);
      setError('Failed to load projects. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const filteredProjects = projects.filter(
    (project) => selectedCategory === 'All' || project.category === selectedCategory
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className="min-h-screen mt-12 bg-gray-50 dark:bg-gray-900 py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl font-bold text-center text-gray-900 dark:text-white mb-8">
          My Projects
        </h1>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300 ${
                selectedCategory === category
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <motion.div
                key={project._id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="relative bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
                onMouseEnter={() => setHoveredProject(project._id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                {/* Project Image */}
                <div className="relative h-48 w-full">
                  <img
                    src={project.images && project.images[0] ? getImageUrl(project.images[0]) : '/placeholder-project.svg'}
                    alt={project.images && project.images[0] ? project.images[0].alt : project.title}
                    className="w-full h-full object-cover"
                  />
                  {hoveredProject === project._id && (
                    <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center space-x-4">
                      {project.githubUrl && (
                        <a
                          key="github-link"
                          href={project.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-white hover:text-primary transition-colors duration-300"
                        >
                          <FaGithub size={24} />
                        </a>
                      )}
                      {project.liveUrl && (
                        <a
                          key="live-link"
                          href={project.liveUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-white hover:text-primary transition-colors duration-300"
                        >
                          <FaExternalLinkAlt size={24} />
                        </a>
                      )}
                    </div>
                  )}
                </div>

                {/* Project Info */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {project.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  <Link
            key={project._id}
            to={`/projects/${project._id}`}
          >View project</Link>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>        
        </div>
      </div>
    </div>
  );
}
