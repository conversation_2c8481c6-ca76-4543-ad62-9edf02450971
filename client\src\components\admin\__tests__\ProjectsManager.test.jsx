import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectsManager from '../ProjectsManager';
import { projectService } from '../../../services/projectService';

// Mock the project service
jest.mock('../../../services/projectService');

describe('ProjectsManager', () => {
  const mockProjects = [
    {
      id: 1,
      title: 'Test Project',
      description: 'Test Description',
      technologies: ['React', 'Node'],
      featured: false
    }
  ];

  beforeEach(() => {
    projectService.getAllProjects.mockResolvedValue(mockProjects);
  });

  test('renders projects list', async () => {
    render(<ProjectsManager />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
  });

  test('opens add project modal', () => {
    render(<ProjectsManager />);
    
    fireEvent.click(screen.getByText('Add Project'));
    expect(screen.getByText('Add New Project')).toBeInTheDocument();
  });

  test('toggles project featured status', async () => {
    const toggleMock = jest.fn();
    projectService.toggleProjectFeatured = toggleMock;

    render(<ProjectsManager />);
    
    await waitFor(() => {
      const toggleButton = screen.getByRole('switch');
      fireEvent.click(toggleButton);
      expect(toggleMock).toHaveBeenCalledWith(1);
    });
  });

  test('deletes project', async () => {
    const deleteMock = jest.fn();
    projectService.deleteProject = deleteMock;

    render(<ProjectsManager />);
    
    await waitFor(() => {
      const deleteButton = screen.getByLabelText('Delete project');
      fireEvent.click(deleteButton);
      const confirmButton = screen.getByText('Confirm Delete');
      fireEvent.click(confirmButton);
      expect(deleteMock).toHaveBeenCalledWith(1);
    });
  });
});
