import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaUniversalAccess, FaEye, FaFont, FaMouse, FaBolt } from 'react-icons/fa';
import { useAccessibility } from '../contexts/AccessibilityContext';

export default function AccessibilityMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const {
    reducedMotion,
    highContrast,
    largeText,
    focusIndicators,
    toggleReducedMotion,
    toggleHighContrast,
    toggleLargeText,
    toggleFocusIndicators,
  } = useAccessibility();

  const menuItems = [
    {
      label: 'Reduced Motion',
      icon: FaBolt,
      checked: reducedMotion,
      onChange: toggleReducedMotion,
      description: 'Minimize animations and transitions',
    },
    {
      label: 'High Contrast',
      icon: FaEye,
      checked: highContrast,
      onChange: toggleHighContrast,
      description: 'Increase color contrast for better visibility',
    },
    {
      label: 'Large Text',
      icon: FaFont,
      checked: largeText,
      onChange: toggleLargeText,
      description: 'Increase text size for better readability',
    },
    {
      label: 'Focus Indicators',
      icon: FaMouse,
      checked: focusIndicators,
      onChange: toggleFocusIndicators,
      description: 'Show visible focus indicators when using keyboard',
    },
  ];

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-12 h-12 bg-indigo-600 text-white rounded-full shadow-lg flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        aria-label="Accessibility Options"
      >
        <FaUniversalAccess className="w-6 h-6" />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-16 right-0 w-72 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Accessibility Options
            </h2>

            <div className="space-y-4">
              {menuItems.map((item) => (
                <div key={item.label} className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      type="checkbox"
                      checked={item.checked}
                      onChange={item.onChange}
                      className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    />
                  </div>
                  <div className="ml-3">
                    <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200">
                      <item.icon className="w-4 h-4 mr-2" />
                      {item.label}
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
