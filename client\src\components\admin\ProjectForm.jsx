import { useState } from 'react';
import { motion } from 'framer-motion';
import { validateProjectForm } from '../../utils/validation';
import ImageUpload from './ImageUpload';
import VideoUpload from './VideoUpload';

export default function ProjectForm({ onSubmit, initialData = null }) {
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    technologies: initialData?.technologies || [],
    image: null,
    githubUrl: initialData?.githubUrl || '',
    liveUrl: initialData?.liveUrl || '',
    featured: initialData?.isFeatured || false,
    category: initialData?.category || '',
    videos: initialData?.videos || []
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState(initialData?.images?.[0]?.url || null);

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    if (type === 'file') {
      const file = files[0];
      setFormData((prev) => ({
        ...prev,
        [name]: file,
      }));
      // Create preview URL
      if (file) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setImagePreview(reader.result);
        };
        reader.readAsDataURL(file);
      }
    } else if (type === 'checkbox') {
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
      }));
    } else if (name === 'technologies') {
      const techArray = value.split(',').map((tech) => tech.trim());
      setFormData((prev) => ({
        ...prev,
        technologies: techArray,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleVideoUpload = (videoData) => {
    if (videoData) {
      setFormData(prev => ({
        ...prev,
        videos: [...(prev.videos || []), videoData]
      }));
    }
  };

  const handleImageUpload = (imageUrl, imageName) => {
    if (imageUrl) {
      setFormData(prev => ({
        ...prev,
        images: [{ url: imageUrl, alt: imageName || 'Project image', isFeatured: true }]
      }));
      setImagePreview(imageUrl);
    } else {
      setFormData(prev => ({
        ...prev,
        images: []
      }));
      setImagePreview(null);
    }
  };

  const handleRemoveVideo = (index) => {
    setFormData(prev => ({
      ...prev,
      videos: prev.videos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const validationErrors = validateProjectForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setLoading(true);
    try {
      // Prepare project data
      const projectData = {
        ...formData,
        isFeatured: formData.featured
      };
      
      await onSubmit(projectData);
    } catch (error) {
      console.error('Error submitting project:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label
          htmlFor="title"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Project Title
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md shadow-sm ${
            errors.title
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
          } dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm`}
        />
        {errors.title && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">
            {errors.title}
          </p>
        )}
      </div>

      <div>
        <label
          htmlFor="description"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Description
        </label>
        <textarea
          id="description"
          name="description"
          rows="4"
          value={formData.description}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md shadow-sm ${
            errors.description
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
          } dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm`}
        />
        {errors.description && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">
            {errors.description}
          </p>
        )}
      </div>

      <div>
        <label
          htmlFor="technologies"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Technologies (comma-separated)
        </label>
        <input
          type="text"
          id="technologies"
          name="technologies"
          value={formData.technologies.join(', ')}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md shadow-sm ${
            errors.technologies
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
          } dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm`}
        />
        {errors.technologies && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">
            {errors.technologies}
          </p>
        )}
      </div>

      <div>
        <ImageUpload 
          label="Project Image" 
          currentImage={imagePreview} 
          onUpload={handleImageUpload} 
          className="mb-4"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Project Video (Optional)
        </label>
        <VideoUpload 
          label="" 
          currentVideo={formData.videos?.[0]?.url} 
          onUpload={handleVideoUpload} 
        />
      </div>

      <div>
        {formData.videos && formData.videos.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Uploaded Videos</h4>
            <div className="space-y-2">
              {formData.videos.map((video, index) => (
                <div key={index} className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded">
                  <div className="flex-1 truncate">
                    <p className="text-sm font-medium">{video.title || 'Untitled Video'}</p>
                    <p className="text-xs text-gray-500 truncate">{video.description || 'No description'}</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveVideo(index)}
                    className="ml-2 p-1 text-red-600 hover:text-red-800 rounded-full hover:bg-red-100 dark:hover:bg-red-900/20"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label
            htmlFor="githubUrl"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            GitHub URL
          </label>
          <input
            type="url"
            id="githubUrl"
            name="githubUrl"
            value={formData.githubUrl}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md shadow-sm ${
              errors.githubUrl
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
            } dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm`}
          />
          {errors.githubUrl && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400">
              {errors.githubUrl}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="liveUrl"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Live URL
          </label>
          <input
            type="url"
            id="liveUrl"
            name="liveUrl"
            value={formData.liveUrl}
            onChange={handleChange}
            className={`mt-1 block w-full rounded-md shadow-sm ${
              errors.liveUrl
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
            } dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm`}
          />
          {errors.liveUrl && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400">
              {errors.liveUrl}
            </p>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label
            htmlFor="category"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Category
          </label>
          <input
            type="text"
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md shadow-sm border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
          />
        </div>
      </div>

      <div className="flex items-center space-x-6">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="featured"
            name="featured"
            checked={formData.featured}
            onChange={handleChange}
            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label
            htmlFor="featured"
            className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
          >
            Featured Project
          </label>
        </div>
      </div>

      <div className="flex justify-end">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          type="submit"
          disabled={loading}
          className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
            loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {loading ? (
            <svg
              className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          ) : null}
          {initialData ? 'Update Project' : 'Create Project'}
        </motion.button>
      </div>
    </form>
  );
}
