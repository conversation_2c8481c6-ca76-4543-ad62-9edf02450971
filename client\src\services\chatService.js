import axios from 'axios';

const API_URL = 'http://localhost:5000/api';

export const chatService = {
  getAllChats: async (token) => {
    const response = await axios.get(`${API_URL}/chat`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  getChatById: async (chatId, token) => {
    const response = await axios.get(`${API_URL}/chat/${chatId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  sendMessage: async (chatId, message, token) => {
    const response = await axios.post(
      `${API_URL}/chat/${chatId}/messages`,
      { content: message },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return response.data;
  },

  markAsRead: async (chatId, token) => {
    const response = await axios.patch(
      `${API_URL}/chat/${chatId}/read`,
      {},
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return response.data;
  },

  deleteChat: async (chatId, token) => {
    await axios.delete(`${API_URL}/chat/${chatId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getUnreadCount: async (token) => {
    const response = await axios.get(`${API_URL}/chat/unread`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data.count;
  }
};
